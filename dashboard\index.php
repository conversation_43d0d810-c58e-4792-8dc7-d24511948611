<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require login
require_login();

$user = get_current_user();
$is_admin = $user['role'] === 'admin';

// Get dashboard statistics
$stats = [];

// Total queries
$stmt = $pdo->query("SELECT COUNT(*) FROM contact_queries");
$stats['total_queries'] = $stmt->fetchColumn();

// New queries (last 7 days)
$stmt = $pdo->query("SELECT COUNT(*) FROM contact_queries WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
$stats['new_queries'] = $stmt->fetchColumn();

// Pending queries
$stmt = $pdo->query("SELECT COUNT(*) FROM contact_queries WHERE status = 'new'");
$stats['pending_queries'] = $stmt->fetchColumn();

if ($is_admin) {
    // Total users
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE status = 'active'");
    $stats['total_users'] = $stmt->fetchColumn();
    
    // Newsletter subscribers
    $stmt = $pdo->query("SELECT COUNT(*) FROM newsletter_subscribers WHERE status = 'active'");
    $stats['newsletter_subscribers'] = $stmt->fetchColumn();
    
    // Blog posts
    $stmt = $pdo->query("SELECT COUNT(*) FROM blog_posts WHERE status = 'published'");
    $stats['blog_posts'] = $stmt->fetchColumn();
}

// Recent queries for this user or all if admin
if ($is_admin) {
    $stmt = $pdo->prepare("
        SELECT cq.*, u.full_name as assigned_name 
        FROM contact_queries cq 
        LEFT JOIN users u ON cq.assigned_to = u.id 
        ORDER BY cq.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute();
} else {
    $stmt = $pdo->prepare("
        SELECT cq.*, u.full_name as assigned_name 
        FROM contact_queries cq 
        LEFT JOIN users u ON cq.assigned_to = u.id 
        WHERE cq.assigned_to = ? OR cq.assigned_to IS NULL 
        ORDER BY cq.created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$user['id']]);
}
$recent_queries = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - WebsiteDeveloper0002.in</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 1rem;
        }
        
        .sidebar-header {
            text-align: center;
            padding: 1rem 0;
            border-bottom: 1px solid #34495e;
            margin-bottom: 1rem;
        }
        
        .sidebar-nav {
            list-style: none;
        }
        
        .sidebar-nav li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            text-decoration: none;
            padding: 0.75rem;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background: #34495e;
        }
        
        .main-content {
            flex: 1;
            padding: 2rem;
            background: #f8f9fa;
        }
        
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            background: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
        }
        
        .recent-queries {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .queries-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .queries-table th,
        .queries-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .queries-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-new {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-in_progress {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-resolved {
            background: #d4edda;
            color: #155724;
        }
        
        .status-closed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .priority-high {
            color: #dc3545;
        }
        
        .priority-medium {
            color: #ffc107;
        }
        
        .priority-low {
            color: #28a745;
        }
        
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .dashboard-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .queries-table {
                font-size: 0.875rem;
            }
            
            .queries-table th,
            .queries-table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>Dashboard</h3>
                <p><?php echo htmlspecialchars($user['full_name']); ?></p>
            </div>
            
            <ul class="sidebar-nav">
                <li><a href="index.php" class="active"><i class="fas fa-home"></i> Dashboard</a></li>
                <li><a href="queries.php"><i class="fas fa-envelope"></i> Queries</a></li>
                <?php if ($is_admin): ?>
                    <li><a href="admin/users.php"><i class="fas fa-users"></i> Users</a></li>
                    <li><a href="admin/blog.php"><i class="fas fa-blog"></i> Blog</a></li>
                    <li><a href="admin/newsletter.php"><i class="fas fa-newspaper"></i> Newsletter</a></li>
                    <li><a href="admin/settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                <?php endif; ?>
                <li><a href="profile.php"><i class="fas fa-user"></i> Profile</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="dashboard-header">
                <div>
                    <h1>Welcome, <?php echo htmlspecialchars($user['full_name']); ?>!</h1>
                    <p>आज का दिन: <?php echo date('d F Y'); ?></p>
                </div>
                <div class="user-info">
                    <span class="status-badge status-<?php echo $user['status']; ?>">
                        <?php echo ucfirst($user['role']); ?>
                    </span>
                    <a href="../index.php" class="btn btn-outline btn-sm">
                        <i class="fas fa-external-link-alt"></i> View Website
                    </a>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-number"><?php echo $stats['total_queries']; ?></div>
                    <div class="stat-label">Total Queries</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-number"><?php echo $stats['pending_queries']; ?></div>
                    <div class="stat-label">Pending Queries</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-number"><?php echo $stats['new_queries']; ?></div>
                    <div class="stat-label">This Week</div>
                </div>
                
                <?php if ($is_admin): ?>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number"><?php echo $stats['total_users']; ?></div>
                        <div class="stat-label">Total Users</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-newspaper"></i>
                        </div>
                        <div class="stat-number"><?php echo $stats['newsletter_subscribers']; ?></div>
                        <div class="stat-label">Newsletter Subscribers</div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-blog"></i>
                        </div>
                        <div class="stat-number"><?php echo $stats['blog_posts']; ?></div>
                        <div class="stat-label">Published Posts</div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Recent Queries -->
            <div class="recent-queries">
                <div class="section-header">
                    <h2>Recent Queries</h2>
                    <a href="queries.php" class="btn btn-primary btn-sm">View All</a>
                </div>
                
                <?php if (empty($recent_queries)): ?>
                    <div style="padding: 2rem; text-align: center; color: #666;">
                        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>कोई queries नहीं मिलीं।</p>
                    </div>
                <?php else: ?>
                    <table class="queries-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Service</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_queries as $query): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($query['name']); ?></strong><br>
                                        <small><?php echo htmlspecialchars($query['email']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($query['service']); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $query['status']; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $query['status'])); ?>
                                        </span>
                                    </td>
                                    <td><?php echo time_ago($query['created_at']); ?></td>
                                    <td>
                                        <a href="queries.php?id=<?php echo $query['id']; ?>" class="btn btn-outline btn-sm">
                                            View
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/main.js"></script>
</body>
</html>
