<?php
// Debug version of index.php to identify the 500 error
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Index Page</h2>";

try {
    echo "<p>1. Starting session...</p>";
    session_start();
    echo "<p style='color: green;'>✅ Session started successfully</p>";
    
    echo "<p>2. Including database config...</p>";
    require_once 'config/database.php';
    echo "<p style='color: green;'>✅ Database config loaded</p>";
    
    echo "<p>3. Testing database connection...</p>";
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    echo "<p>4. Including functions...</p>";
    require_once 'includes/functions.php';
    echo "<p style='color: green;'>✅ Functions loaded</p>";
    
    echo "<p>5. Testing get_services function...</p>";
    try {
        $services = get_services();
        echo "<p style='color: green;'>✅ get_services() worked. Found " . count($services) . " services</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ get_services() failed: " . $e->getMessage() . "</p>";
        echo "<p>This suggests the 'services' table doesn't exist. Creating mock data...</p>";
        $services = [];
    }
    
    echo "<p>6. Testing get_blog_posts function...</p>";
    try {
        $recent_posts = get_blog_posts(3);
        echo "<p style='color: green;'>✅ get_blog_posts() worked. Found " . count($recent_posts) . " posts</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ get_blog_posts() failed: " . $e->getMessage() . "</p>";
        echo "<p>This suggests the 'blog_posts' table doesn't exist. Creating mock data...</p>";
        $recent_posts = [];
    }
    
    echo "<p>7. All checks passed! The issue is likely missing database tables.</p>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Import the database schema from database/schema.sql</li>";
    echo "<li>Or create the tables manually</li>";
    echo "<li>Then try accessing the main website</li>";
    echo "</ol>";
    
    echo "<h3>Quick Fix - Create Essential Tables:</h3>";
    echo "<p><a href='create-tables.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Database Tables</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error occurred:</strong></p>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    
    if ($e->getCode() == 1049) {
        echo "<h3>Database Not Found!</h3>";
        echo "<p>The database 'xozhvpdr_websitedeveloper0002' doesn't exist.</p>";
        echo "<p>Please create it in your hosting control panel first.</p>";
    }
}

echo "<hr>";
echo "<p><a href='test-connection.php'>Test Database Connection</a> | <a href='test-syntax.php'>Test PHP Syntax</a></p>";
?>
