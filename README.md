# WebsiteDeveloper0002.in - Complete Web Development Services Website

A comprehensive website for web development services in India with pricing plans, blog system, payment integration, and customer management dashboard.

## 🚀 Features

### Frontend Features
- **Responsive Design**: Mobile-first approach with modern UI/UX
- **Multi-language Support**: Hindi and English content
- **Service Pages**: Business Premium (₹15,000), E-commerce (₹75,000), Custom Development
- **Blog System**: SEO-optimized blog with categories and search
- **Contact Forms**: With Google reCAPTCHA integration
- **Newsletter Subscription**: Email marketing integration
- **Payment Integration**: Razorpay payment gateway
- **SEO Optimized**: Meta tags, structured data, and analytics

### Backend Features
- **User Authentication**: Secure login/register system
- **Dashboard System**: Customer and admin dashboards
- **Query Management**: Contact inquiry tracking and response system
- **Order Management**: Payment tracking and order processing
- **Email Automation**: Automated emails for confirmations and notifications
- **Blog Management**: Content management system for blog posts
- **Analytics Integration**: Google Analytics and conversion tracking

### Security Features
- **SQL Injection Protection**: PDO prepared statements
- **XSS Protection**: Input sanitization and output encoding
- **CSRF Protection**: Session-based security
- **Password Hashing**: Secure password storage
- **Rate Limiting**: Protection against spam and abuse
- **SSL Ready**: HTTPS configuration support

## 📋 Requirements

- **PHP**: 7.4 or higher
- **MySQL**: 5.7 or higher
- **Web Server**: Apache/Nginx
- **Extensions**: PDO, cURL, OpenSSL, mbstring
- **Composer**: For dependency management (optional)

## 🛠️ Installation

### 1. Download and Setup
```bash
# Clone or download the project
git clone https://github.com/your-repo/websitedeveloper0002.in.git
cd websitedeveloper0002.in

# Set proper permissions
chmod 755 -R .
chmod 777 uploads/
```

### 2. Database Setup
```sql
-- Import the database schema
mysql -u root -p < database/schema.sql

-- Or manually create database and import
CREATE DATABASE websitedeveloper0002;
USE websitedeveloper0002;
SOURCE database/schema.sql;
```

### 3. Configuration
Edit `config/database.php`:
```php
// Database settings
define('DB_HOST', 'localhost');
define('DB_USER', 'your_db_user');
define('DB_PASS', 'your_db_password');
define('DB_NAME', 'websitedeveloper0002');

// Site settings
define('SITE_URL', 'https://your-domain.com');
define('SITE_EMAIL', '<EMAIL>');

// Razorpay settings
define('RAZORPAY_KEY_ID', 'your_razorpay_key_id');
define('RAZORPAY_KEY_SECRET', 'your_razorpay_key_secret');

// reCAPTCHA settings
define('RECAPTCHA_SITE_KEY', 'your_recaptcha_site_key');
define('RECAPTCHA_SECRET_KEY', 'your_recaptcha_secret_key');

// Email settings (SMTP)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your_app_password');
```

### 4. Third-Party Services Setup

#### Razorpay Payment Gateway
1. Sign up at [Razorpay](https://razorpay.com/)
2. Get API keys from Dashboard
3. Update `RAZORPAY_KEY_ID` and `RAZORPAY_KEY_SECRET` in config

#### Google reCAPTCHA
1. Get keys from [Google reCAPTCHA](https://www.google.com/recaptcha/)
2. Update `RECAPTCHA_SITE_KEY` and `RECAPTCHA_SECRET_KEY` in config

#### Google Analytics
1. Create property at [Google Analytics](https://analytics.google.com/)
2. Add tracking ID to settings table or directly in templates

### 5. Email Configuration
For Gmail SMTP:
1. Enable 2-factor authentication
2. Generate app password
3. Use app password in SMTP_PASSWORD

## 📁 Project Structure

```
websitedeveloper0002.in/
├── assets/
│   ├── css/
│   │   ├── style.css
│   │   └── responsive.css
│   ├── js/
│   │   └── main.js
│   └── images/
├── config/
│   └── database.php
├── dashboard/
│   ├── index.php
│   ├── login.php
│   ├── queries.php
│   └── admin/
├── database/
│   └── schema.sql
├── includes/
│   ├── functions.php
│   ├── contact-handler.php
│   └── newsletter-handler.php
├── legal/
│   ├── privacy-policy.php
│   ├── terms-conditions.php
│   ├── cookie-policy.php
│   └── refund-policy.php
├── payment/
│   ├── razorpay-handler.php
│   └── success.php
├── services/
│   ├── business-premium.php
│   ├── ecommerce.php
│   └── custom-development.php
├── uploads/
├── index.php
├── blog.php
└── README.md
```

## 🔧 Configuration Options

### Database Settings
- Update connection details in `config/database.php`
- Default admin login: <EMAIL> / password

### Site Customization
- Logo: Replace `assets/images/logo.png`
- Colors: Modify CSS variables in `assets/css/style.css`
- Content: Update text in PHP files
- Services: Modify pricing and features in database

### Email Templates
- Templates stored in `email_templates` table
- Customize in admin dashboard
- Variables: {{name}}, {{email}}, {{message}}, etc.

## 🚀 Usage

### Admin Dashboard
1. Login at `/dashboard/login.php`
2. Default admin: <EMAIL>
3. Manage queries, users, blog posts, settings

### Customer Features
- Contact forms with automatic responses
- Service inquiry and payment
- Newsletter subscription
- Blog reading and search

### Payment Processing
1. Customer selects service and fills form
2. Razorpay payment gateway integration
3. Automatic order creation and email notifications
4. Admin notification for follow-up

## 🔒 Security Best Practices

### Implemented Security
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- CSRF tokens
- Rate limiting
- Secure password hashing

### Additional Recommendations
- Use HTTPS in production
- Regular security updates
- Strong database passwords
- Backup strategy
- Monitor error logs

## 📊 SEO Features

- Meta tags optimization
- Structured data markup
- XML sitemap generation
- Google Analytics integration
- Page speed optimization
- Mobile-friendly design

## 🐛 Troubleshooting

### Common Issues

**Database Connection Error**
- Check database credentials in config
- Ensure MySQL service is running
- Verify database exists

**Email Not Sending**
- Check SMTP settings
- Verify app password for Gmail
- Check server firewall settings

**Payment Issues**
- Verify Razorpay API keys
- Check webhook configuration
- Monitor Razorpay dashboard

**reCAPTCHA Not Working**
- Verify site and secret keys
- Check domain configuration
- Ensure HTTPS in production

## 📈 Performance Optimization

- Enable gzip compression
- Optimize images
- Use CDN for assets
- Enable browser caching
- Minify CSS/JS files

## 🔄 Updates and Maintenance

### Regular Tasks
- Update PHP and MySQL
- Monitor error logs
- Backup database
- Check security updates
- Review analytics data

### Database Maintenance
```sql
-- Optimize tables
OPTIMIZE TABLE contact_queries, orders, blog_posts;

-- Clean old logs
DELETE FROM email_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
```

## 📞 Support

For technical support or customization:
- Email: <EMAIL>
- Phone: +91 XXXXXXXXXX
- WhatsApp: +91 XXXXXXXXXX

## 📄 License

This project is proprietary software for WebsiteDeveloper0002.in. All rights reserved.

## 🤝 Contributing

This is a private project. For feature requests or bug reports, please contact the development team.

---

**WebsiteDeveloper0002.in** - Professional Web Development Services for Indian Businesses
