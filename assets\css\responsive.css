/* Responsive Design */

/* Large screens */
@media (max-width: 1200px) {
    .container {
        max-width: 960px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
}

/* Medium screens */
@media (max-width: 992px) {
    .container {
        max-width: 720px;
    }
    
    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .hero-title {
        font-size: 2.2rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .pricing-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Small screens */
@media (max-width: 768px) {
    .container {
        max-width: 540px;
        padding: 0 15px;
    }
    
    /* Navigation */
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        z-index: 999;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-list {
        flex-direction: column;
        gap: 0;
    }
    
    .nav-list li {
        padding: 1rem 0;
        border-bottom: 1px solid #eee;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }
    
    .nav-toggle.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
    
    /* Hero Section */
    .hero {
        padding: 100px 0 60px;
        min-height: auto;
    }
    
    .hero-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }
    
    .hero-features {
        justify-content: center;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }
    
    /* Sections */
    section {
        padding: 60px 0;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .section-subtitle {
        font-size: 1rem;
    }
    
    /* Pricing */
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .pricing-card.featured {
        transform: none;
    }
    
    .pricing-card:hover {
        transform: translateY(-5px);
    }
    
    .plan-price .amount {
        font-size: 2.5rem;
    }
    
    /* Services */
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    /* Contact */
    .contact-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* Extra small screens */
@media (max-width: 576px) {
    .container {
        max-width: 100%;
        padding: 0 10px;
    }
    
    .hero-title {
        font-size: 1.8rem;
    }
    
    .section-title {
        font-size: 1.6rem;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 14px;
    }
    
    .service-card,
    .pricing-card {
        padding: 1.5rem;
    }
    
    .contact-form {
        padding: 1rem;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px;
        font-size: 14px;
    }
    
    .plan-price .amount {
        font-size: 2rem;
    }
    
    .hero-buttons {
        width: 100%;
    }
    
    .hero-buttons .btn {
        width: 100%;
    }
}

/* Print styles */
@media print {
    .header,
    .footer,
    .nav-toggle,
    .hero-buttons,
    .pricing-actions,
    .contact-form {
        display: none;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
    }
    
    .hero {
        padding: 20px 0;
        background: none;
    }
    
    .section-title {
        font-size: 18pt;
        margin-bottom: 10pt;
    }
    
    .pricing-card,
    .service-card {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .btn-primary {
        background: #000;
        border: 2px solid #000;
    }
    
    .btn-secondary {
        background: #fff;
        color: #000;
        border: 2px solid #000;
    }
    
    .highlight {
        background: #000;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    .header {
        background: rgba(26, 26, 26, 0.95);
    }
    
    .nav-link {
        color: #e0e0e0;
    }
    
    .hero {
        background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    }
    
    .hero-title,
    .section-title {
        color: #e0e0e0;
    }
    
    .service-card,
    .pricing-card,
    .contact-form {
        background: #2d3748;
        color: #e0e0e0;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        background: #4a5568;
        border-color: #718096;
        color: #e0e0e0;
    }
    
    .services {
        background: #2d3748;
    }
    
    .contact {
        background: #2d3748;
    }
}
