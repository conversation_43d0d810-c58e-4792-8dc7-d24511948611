<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Log the logout action
if (isset($_SESSION['user_id'])) {
    $user = get_current_user();
    if ($user) {
        error_log("User logout: {$user['email']} - {$user['role']}");
    }
}

// Clear remember me cookie if it exists
if (isset($_COOKIE['remember_token'])) {
    // Remove token from database
    if (isset($_SESSION['user_id'])) {
        global $pdo;
        $stmt = $pdo->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
    }
    
    // Clear the cookie
    setcookie('remember_token', '', time() - 3600, '/', '', true, true);
}

// Destroy session
session_destroy();

// Clear session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Redirect to login page with success message
header('Location: login.php?message=logged_out');
exit;
?>
