<?php
session_start();
require_once '../config/database.php';
require_once 'functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    json_response(['success' => false, 'message' => 'Invalid request method'], 405);
}

try {
    // Get and sanitize form data
    $name = sanitize_input($_POST['name'] ?? '');
    $email = sanitize_input($_POST['email'] ?? '');
    $phone = sanitize_input($_POST['phone'] ?? '');
    $service = sanitize_input($_POST['service'] ?? '');
    $message = sanitize_input($_POST['message'] ?? '');
    $recaptcha_response = $_POST['g-recaptcha-response'] ?? '';
    
    // Validation
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'कृपया अपना नाम दर्ज करें।';
    }
    
    if (empty($email) || !validate_email($email)) {
        $errors[] = 'कृपया वैध ईमेल पता दर्ज करें।';
    }
    
    if (empty($phone) || !validate_phone($phone)) {
        $errors[] = 'कृपया वैध फोन नंबर दर्ज करें।';
    }
    
    if (empty($service)) {
        $errors[] = 'कृपया सेवा चुनें।';
    }
    
    if (empty($message)) {
        $errors[] = 'कृपया अपना संदेश दर्ज करें।';
    }
    
    // Verify reCAPTCHA if enabled
    $recaptcha_enabled = get_setting('recaptcha_enabled', 'true') === 'true';
    if ($recaptcha_enabled && !verify_recaptcha($recaptcha_response)) {
        $errors[] = 'कृपया reCAPTCHA को पूरा करें।';
    }
    
    // Rate limiting - check if same IP has submitted in last 5 minutes
    $client_ip = get_client_ip();
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM contact_queries WHERE ip_address = ? AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)");
    $stmt->execute([$client_ip]);
    $recent_submissions = $stmt->fetchColumn();
    
    if ($recent_submissions >= 3) {
        $errors[] = 'बहुत सारे अनुरोध। कृपया 5 मिनट बाद पुनः प्रयास करें।';
    }
    
    // If there are validation errors, return them
    if (!empty($errors)) {
        json_response(['success' => false, 'message' => implode(' ', $errors)], 400);
    }
    
    // Insert contact query into database
    $stmt = $pdo->prepare("
        INSERT INTO contact_queries (name, email, phone, service, message, ip_address, user_agent) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $result = $stmt->execute([$name, $email, $phone, $service, $message, $client_ip, $user_agent]);
    
    if (!$result) {
        throw new Exception('Database insertion failed');
    }
    
    $query_id = $pdo->lastInsertId();
    
    // Send thank you email to customer
    $email_template = get_email_template('contact-thank-you', [
        'name' => $name,
        'email' => $email,
        'message' => $message,
        'service' => $service
    ]);
    
    if ($email_template) {
        send_email($email, $email_template['subject'], $email_template['body'], $email_template['template_id']);
    }
    
    // Send notification email to admin
    $admin_email = get_setting('contact_email', '<EMAIL>');
    $admin_subject = 'नई संपर्क पूछताछ - ' . $name;
    $admin_body = "
        <h2>नई संपर्क पूछताछ प्राप्त हुई</h2>
        <p><strong>नाम:</strong> {$name}</p>
        <p><strong>ईमेल:</strong> {$email}</p>
        <p><strong>फोन:</strong> {$phone}</p>
        <p><strong>सेवा:</strong> {$service}</p>
        <p><strong>संदेश:</strong></p>
        <p>{$message}</p>
        <p><strong>IP Address:</strong> {$client_ip}</p>
        <p><strong>समय:</strong> " . date('Y-m-d H:i:s') . "</p>
        <hr>
        <p><a href='" . SITE_URL . "/dashboard/queries.php?id={$query_id}'>Query को Dashboard में देखें</a></p>
    ";
    
    send_email($admin_email, $admin_subject, $admin_body);
    
    // Send WhatsApp notification (if WhatsApp API is configured)
    $whatsapp_number = get_setting('whatsapp_number');
    if ($whatsapp_number) {
        // You can integrate WhatsApp Business API here
        // For now, we'll just log it
        error_log("WhatsApp notification: New contact query from {$name} - {$phone}");
    }
    
    // Log successful submission
    error_log("Contact form submitted successfully: {$name} - {$email} - {$service}");
    
    // Return success response
    json_response([
        'success' => true,
        'message' => 'आपका संदेश सफलतापूर्वक भेज दिया गया है! हम जल्द ही आपसे संपर्क करेंगे।',
        'query_id' => $query_id
    ]);
    
} catch (Exception $e) {
    // Log error
    error_log("Contact form error: " . $e->getMessage());
    
    // Return error response
    json_response([
        'success' => false,
        'message' => 'कुछ तकनीकी समस्या हुई है। कृपया बाद में पुनः प्रयास करें या सीधे फोन करें।'
    ], 500);
}
?>
