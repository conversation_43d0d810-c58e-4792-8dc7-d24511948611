<?php
// PHP Syntax and Error Test
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>PHP Syntax Test</h2>";

$files_to_test = [
    'index.php',
    'config/database.php',
    'includes/functions.php',
    'includes/contact-handler.php',
    'blog.php'
];

echo "<h3>Testing PHP Files for Syntax Errors:</h3>";

foreach ($files_to_test as $file) {
    if (file_exists($file)) {
        echo "<p>Testing <strong>$file</strong>: ";
        
        // Check syntax
        $output = shell_exec("php -l $file 2>&1");
        
        if (strpos($output, 'No syntax errors') !== false) {
            echo "<span style='color: green;'>✅ OK</span></p>";
        } else {
            echo "<span style='color: red;'>❌ Syntax Error</span></p>";
            echo "<pre style='background: #ffe6e6; padding: 10px; border-radius: 5px;'>$output</pre>";
        }
    } else {
        echo "<p>File <strong>$file</strong>: <span style='color: orange;'>⚠️ Not Found</span></p>";
    }
}

echo "<hr>";
echo "<h3>Server Information:</h3>";
echo "<p>Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p>Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p>Current Directory: " . getcwd() . "</p>";

echo "<hr>";
echo "<h3>Error Log Check:</h3>";
$error_log = ini_get('error_log');
if ($error_log && file_exists($error_log)) {
    echo "<p>Error log location: $error_log</p>";
    $recent_errors = shell_exec("tail -10 $error_log 2>&1");
    if ($recent_errors) {
        echo "<h4>Recent Errors:</h4>";
        echo "<pre style='background: #ffe6e6; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>$recent_errors</pre>";
    }
} else {
    echo "<p>Error log not found or not configured.</p>";
}

echo "<hr>";
echo "<p><a href='test-connection.php'>Test Database Connection</a> | <a href='index.php'>← Back to Website</a></p>";
?>
