<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    json_response(['success' => false, 'message' => 'Invalid request method'], 405);
}

try {
    // Get and validate input
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create_order':
            createRazorpayOrder();
            break;
            
        case 'verify_payment':
            verifyRazorpayPayment();
            break;
            
        default:
            json_response(['success' => false, 'message' => 'Invalid action'], 400);
    }
    
} catch (Exception $e) {
    error_log("Razorpay handler error: " . $e->getMessage());
    json_response(['success' => false, 'message' => 'Payment processing error'], 500);
}

function createRazorpayOrder() {
    global $pdo;
    
    // Get form data
    $service_id = intval($_POST['service_id'] ?? 0);
    $customer_name = sanitize_input($_POST['customer_name'] ?? '');
    $customer_email = sanitize_input($_POST['customer_email'] ?? '');
    $customer_phone = sanitize_input($_POST['customer_phone'] ?? '');
    $notes = sanitize_input($_POST['notes'] ?? '');
    
    // Validation
    if (!$service_id || !$customer_name || !$customer_email || !$customer_phone) {
        json_response(['success' => false, 'message' => 'सभी required fields भरें।'], 400);
    }
    
    if (!validate_email($customer_email)) {
        json_response(['success' => false, 'message' => 'Valid email address दर्ज करें।'], 400);
    }
    
    if (!validate_phone($customer_phone)) {
        json_response(['success' => false, 'message' => 'Valid phone number दर्ज करें।'], 400);
    }
    
    // Get service details
    $stmt = $pdo->prepare("SELECT * FROM services WHERE id = ? AND status = 'active'");
    $stmt->execute([$service_id]);
    $service = $stmt->fetch();
    
    if (!$service) {
        json_response(['success' => false, 'message' => 'Invalid service selected.'], 400);
    }
    
    // Generate order number
    $order_number = 'WD' . date('Ymd') . rand(1000, 9999);
    
    // Create order in database
    $stmt = $pdo->prepare("
        INSERT INTO orders (order_number, customer_name, customer_email, customer_phone, service_id, amount, notes) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $order_number,
        $customer_name,
        $customer_email,
        $customer_phone,
        $service_id,
        $service['price'],
        $notes
    ]);
    
    $order_id = $pdo->lastInsertId();
    
    // Create Razorpay order
    $razorpay_order = createRazorpayOrderAPI([
        'amount' => $service['price'] * 100, // Amount in paise
        'currency' => 'INR',
        'receipt' => $order_number,
        'notes' => [
            'order_id' => $order_id,
            'service' => $service['name'],
            'customer_email' => $customer_email
        ]
    ]);
    
    if (!$razorpay_order) {
        json_response(['success' => false, 'message' => 'Razorpay order creation failed.'], 500);
    }
    
    // Update order with Razorpay order ID
    $stmt = $pdo->prepare("UPDATE orders SET razorpay_order_id = ? WHERE id = ?");
    $stmt->execute([$razorpay_order['id'], $order_id]);
    
    // Return order details for frontend
    json_response([
        'success' => true,
        'order_id' => $razorpay_order['id'],
        'amount' => $service['price'],
        'currency' => 'INR',
        'name' => 'WebsiteDeveloper0002.in',
        'description' => $service['name'],
        'customer' => [
            'name' => $customer_name,
            'email' => $customer_email,
            'contact' => $customer_phone
        ],
        'notes' => [
            'order_number' => $order_number,
            'service' => $service['name']
        ]
    ]);
}

function verifyRazorpayPayment() {
    global $pdo;
    
    // Get payment data
    $razorpay_order_id = $_POST['razorpay_order_id'] ?? '';
    $razorpay_payment_id = $_POST['razorpay_payment_id'] ?? '';
    $razorpay_signature = $_POST['razorpay_signature'] ?? '';
    
    if (!$razorpay_order_id || !$razorpay_payment_id || !$razorpay_signature) {
        json_response(['success' => false, 'message' => 'Payment verification data missing.'], 400);
    }
    
    // Verify signature
    $expected_signature = hash_hmac('sha256', $razorpay_order_id . '|' . $razorpay_payment_id, RAZORPAY_KEY_SECRET);
    
    if (!hash_equals($expected_signature, $razorpay_signature)) {
        json_response(['success' => false, 'message' => 'Payment signature verification failed.'], 400);
    }
    
    // Get order from database
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE razorpay_order_id = ?");
    $stmt->execute([$razorpay_order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        json_response(['success' => false, 'message' => 'Order not found.'], 404);
    }
    
    // Verify payment with Razorpay API
    $payment_details = getRazorpayPayment($razorpay_payment_id);
    
    if (!$payment_details || $payment_details['status'] !== 'captured') {
        json_response(['success' => false, 'message' => 'Payment verification failed.'], 400);
    }
    
    // Update order status
    $stmt = $pdo->prepare("
        UPDATE orders 
        SET payment_status = 'paid', 
            payment_method = 'razorpay', 
            payment_id = ?, 
            razorpay_payment_id = ?,
            order_status = 'confirmed'
        WHERE id = ?
    ");
    $stmt->execute([$razorpay_payment_id, $razorpay_payment_id, $order['id']]);
    
    // Send confirmation emails
    sendPaymentConfirmationEmails($order, $payment_details);
    
    // Log successful payment
    error_log("Payment successful: Order #{$order['order_number']} - Amount: ₹{$order['amount']} - Customer: {$order['customer_email']}");
    
    json_response([
        'success' => true,
        'message' => 'Payment successful!',
        'order_number' => $order['order_number'],
        'amount' => $order['amount'],
        'redirect_url' => '../payment/success.php?order=' . $order['order_number']
    ]);
}

function createRazorpayOrderAPI($order_data) {
    $url = 'https://api.razorpay.com/v1/orders';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($order_data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Basic ' . base64_encode(RAZORPAY_KEY_ID . ':' . RAZORPAY_KEY_SECRET)
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        return json_decode($response, true);
    }
    
    error_log("Razorpay order creation failed: " . $response);
    return false;
}

function getRazorpayPayment($payment_id) {
    $url = "https://api.razorpay.com/v1/payments/{$payment_id}";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Basic ' . base64_encode(RAZORPAY_KEY_ID . ':' . RAZORPAY_KEY_SECRET)
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        return json_decode($response, true);
    }
    
    return false;
}

function sendPaymentConfirmationEmails($order, $payment_details) {
    global $pdo;
    
    // Get service details
    $stmt = $pdo->prepare("SELECT * FROM services WHERE id = ?");
    $stmt->execute([$order['service_id']]);
    $service = $stmt->fetch();
    
    // Send confirmation email to customer
    $customer_subject = 'Payment Confirmation - Order #' . $order['order_number'];
    $customer_body = "
        <h2>Payment Successful!</h2>
        <p>Dear {$order['customer_name']},</p>
        <p>आपका payment successfully process हो गया है। Order details:</p>
        
        <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
            <h3>Order Details</h3>
            <p><strong>Order Number:</strong> {$order['order_number']}</p>
            <p><strong>Service:</strong> {$service['name']}</p>
            <p><strong>Amount:</strong> ₹" . number_format($order['amount']) . "</p>
            <p><strong>Payment ID:</strong> {$payment_details['id']}</p>
            <p><strong>Payment Method:</strong> " . ucfirst($payment_details['method']) . "</p>
        </div>
        
        <h3>Next Steps:</h3>
        <ul>
            <li>हमारी team आपसे 24 घंटे के अंदर संपर्क करेगी</li>
            <li>Project requirements discuss करने के लिए</li>
            <li>Development timeline share करेंगे</li>
        </ul>
        
        <p>किसी भी query के लिए हमसे संपर्क करें:</p>
        <p>Email: <EMAIL></p>
        <p>Phone: +91 XXXXXXXXXX</p>
        
        <p>Thank you for choosing WebsiteDeveloper0002.in!</p>
    ";
    
    send_email($order['customer_email'], $customer_subject, $customer_body);
    
    // Send notification email to admin
    $admin_email = get_setting('contact_email', '<EMAIL>');
    $admin_subject = 'New Payment Received - Order #' . $order['order_number'];
    $admin_body = "
        <h2>New Payment Received!</h2>
        
        <div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0;'>
            <h3>Order Details</h3>
            <p><strong>Order Number:</strong> {$order['order_number']}</p>
            <p><strong>Customer:</strong> {$order['customer_name']}</p>
            <p><strong>Email:</strong> {$order['customer_email']}</p>
            <p><strong>Phone:</strong> {$order['customer_phone']}</p>
            <p><strong>Service:</strong> {$service['name']}</p>
            <p><strong>Amount:</strong> ₹" . number_format($order['amount']) . "</p>
        </div>
        
        <div style='background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;'>
            <h3>Payment Details</h3>
            <p><strong>Payment ID:</strong> {$payment_details['id']}</p>
            <p><strong>Method:</strong> " . ucfirst($payment_details['method']) . "</p>
            <p><strong>Status:</strong> " . ucfirst($payment_details['status']) . "</p>
            <p><strong>Time:</strong> " . date('Y-m-d H:i:s') . "</p>
        </div>
        
        <p><strong>Action Required:</strong> Contact customer within 24 hours to discuss project requirements.</p>
        
        <p><a href='" . SITE_URL . "/dashboard/orders.php?id={$order['id']}' style='background: #667eea; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Order Details</a></p>
    ";
    
    send_email($admin_email, $admin_subject, $admin_body);
}
?>
