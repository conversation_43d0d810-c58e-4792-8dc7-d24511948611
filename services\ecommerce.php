<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Get service details
$service = get_service_by_slug('ecommerce');
if (!$service) {
    header('HTTP/1.1 404 Not Found');
    include '../404.php';
    exit;
}

$features = json_decode($service['features'], true);
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E-commerce Website Development - ₹75,000 | WebsiteDeveloper0002.in</title>
    <meta name="description" content="Complete e-commerce website with payment gateway, inventory management, and multi-vendor support. Starting at ₹75,000 with free domain & hosting.">
    <meta name="keywords" content="ecommerce website, online store, payment gateway, multi-vendor, inventory management, shopping cart">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <style>
        .service-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
        }
        
        .popular-badge {
            background: #ff6b6b;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: inline-block;
        }
        
        .service-content {
            padding: 80px 0;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }
        
        .pricing-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 100px;
            border: 3px solid #667eea;
        }
        
        .price-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .price-amount {
            font-size: 3rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .price-period {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .features-list {
            list-style: none;
            margin-bottom: 2rem;
        }
        
        .features-list li {
            padding: 0.75rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .features-list li:last-child {
            border-bottom: none;
        }
        
        .features-list i {
            color: #28a745;
            font-size: 1.1rem;
        }
        
        .payment-form {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .comparison-section {
            background: #f8f9fa;
            padding: 80px 0;
        }
        
        .comparison-table {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .comparison-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 1rem;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .comparison-table th {
            background: #667eea;
            color: white;
            font-weight: 600;
        }
        
        .comparison-table .feature-name {
            text-align: left;
            font-weight: 500;
        }
        
        .check-icon {
            color: #28a745;
            font-size: 1.2rem;
        }
        
        .cross-icon {
            color: #dc3545;
            font-size: 1.2rem;
        }
        
        @media (max-width: 768px) {
            .service-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .pricing-card {
                position: static;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .comparison-table {
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="../index.php">
                        <span class="brand-text">WebsiteDeveloper0002.in</span>
                    </a>
                </div>
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li><a href="../index.php" class="nav-link">होम</a></li>
                        <li><a href="../index.php#services" class="nav-link">सेवाएं</a></li>
                        <li><a href="../index.php#pricing" class="nav-link">प्राइसिंग</a></li>
                        <li><a href="../blog.php" class="nav-link">ब्लॉग</a></li>
                        <li><a href="../index.php#contact" class="nav-link">संपर्क</a></li>
                        <li><a href="../dashboard/login.php" class="nav-link">लॉगिन</a></li>
                    </ul>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Service Hero -->
    <section class="service-hero">
        <div class="container">
            <div class="popular-badge">
                <i class="fas fa-star"></i> Most Popular
            </div>
            <h1>E-commerce Website</h1>
            <p>Complete online store with payment gateway और multi-vendor support</p>
            <div class="hero-features">
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Payment Gateway</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Multi-Vendor Support</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Inventory Management</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Service Content -->
    <section class="service-content">
        <div class="container">
            <div class="service-grid">
                <div class="service-main">
                    <h2>Complete E-commerce Solution</h2>
                    <p>हमारा E-commerce package आपके online business के लिए एक complete solution है। इसमें product management से लेकर payment processing तक सब कुछ शामिल है।</p>
                    
                    <h3>E-commerce Features:</h3>
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <h4>Shopping Cart</h4>
                            <p>Advanced shopping cart with wishlist, compare, और quick checkout features।</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <h4>Payment Gateway</h4>
                            <p>Razorpay integration with multiple payment options और secure transactions।</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <h4>Inventory Management</h4>
                            <p>Complete stock management with low stock alerts और automatic updates।</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h4>Multi-Vendor Support</h4>
                            <p>Multiple sellers can list their products with separate dashboards।</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-truck"></i>
                            </div>
                            <h4>Order Management</h4>
                            <p>Complete order tracking from placement to delivery।</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <h4>Sales Analytics</h4>
                            <p>Detailed sales reports और performance analytics।</p>
                        </div>
                    </div>
                    
                    <h3>Dashboard Features</h3>
                    <ul class="features-list">
                        <li><i class="fas fa-check"></i> Admin Dashboard - Complete store management</li>
                        <li><i class="fas fa-check"></i> Vendor Dashboard - Product और order management</li>
                        <li><i class="fas fa-check"></i> Customer Dashboard - Order history और profile</li>
                        <li><i class="fas fa-check"></i> Product Categories और subcategories</li>
                        <li><i class="fas fa-check"></i> Coupon और discount management</li>
                        <li><i class="fas fa-check"></i> Customer reviews और ratings</li>
                        <li><i class="fas fa-check"></i> Email notifications for orders</li>
                        <li><i class="fas fa-check"></i> Mobile responsive design</li>
                    </ul>
                </div>
                
                <div class="service-sidebar">
                    <div class="pricing-card">
                        <div class="price-header">
                            <div class="price-amount">₹75,000</div>
                            <div class="price-period">one-time payment</div>
                            <p>Complete e-commerce setup</p>
                        </div>
                        
                        <ul class="features-list">
                            <?php foreach ($features as $feature): ?>
                                <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <button onclick="openPaymentForm()" class="btn btn-primary" style="width: 100%; margin-bottom: 1rem;">
                            <i class="fas fa-shopping-cart"></i> Start My Store
                        </button>
                        <a href="tel:+91XXXXXXXXXX" class="btn btn-outline" style="width: 100%;">
                            <i class="fas fa-phone"></i> Call Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Comparison Section -->
    <section class="comparison-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Package Comparison</h2>
                <p class="section-subtitle">देखें कि E-commerce package में क्या extra मिलता है</p>
            </div>
            
            <div class="comparison-table">
                <table>
                    <thead>
                        <tr>
                            <th>Features</th>
                            <th>Business Premium<br><small>₹15,000</small></th>
                            <th>E-commerce<br><small>₹75,000</small></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="feature-name">Multi-page Website</td>
                            <td><i class="fas fa-check check-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Contact Forms</td>
                            <td><i class="fas fa-check check-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Shopping Cart</td>
                            <td><i class="fas fa-times cross-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Payment Gateway</td>
                            <td><i class="fas fa-times cross-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Product Management</td>
                            <td><i class="fas fa-times cross-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Inventory Management</td>
                            <td><i class="fas fa-times cross-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Multi-Vendor Support</td>
                            <td><i class="fas fa-times cross-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Order Management</td>
                            <td><i class="fas fa-times cross-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                        <tr>
                            <td class="feature-name">Sales Analytics</td>
                            <td><i class="fas fa-times cross-icon"></i></td>
                            <td><i class="fas fa-check check-icon"></i></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </section>

    <!-- Payment Form Modal -->
    <div id="paymentModal" class="query-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Start Your E-commerce Store</h2>
                <button class="close-modal" onclick="closePaymentModal()">&times;</button>
            </div>
            
            <div class="payment-form">
                <form id="ecommerceForm">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="customer_name">आपका नाम *</label>
                            <input type="text" id="customer_name" name="customer_name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="customer_email">ईमेल एड्रेस *</label>
                            <input type="email" id="customer_email" name="customer_email" required>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="customer_phone">फोन नंबर *</label>
                            <input type="tel" id="customer_phone" name="customer_phone" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="business_type">Business Type</label>
                            <select id="business_type" name="business_type">
                                <option value="">Select Business Type</option>
                                <option value="fashion">Fashion & Clothing</option>
                                <option value="electronics">Electronics</option>
                                <option value="food">Food & Beverages</option>
                                <option value="books">Books & Stationery</option>
                                <option value="health">Health & Beauty</option>
                                <option value="home">Home & Garden</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="store_requirements">Store Requirements *</label>
                        <textarea id="store_requirements" name="notes" rows="4" placeholder="बताएं कि आपको कैसा online store चाहिए, कितने products होंगे, etc..." required></textarea>
                    </div>
                    
                    <input type="hidden" name="service_id" value="<?php echo $service['id']; ?>">
                    
                    <div class="form-group">
                        <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                    </div>
                    
                    <div style="background: #e3f2fd; padding: 1rem; border-radius: 5px; margin: 1rem 0;">
                        <h4 style="color: #1976d2; margin-bottom: 0.5rem;">Payment Summary:</h4>
                        <p style="margin: 0; color: #1976d2;"><strong>Amount: ₹75,000</strong> (one-time payment)</p>
                        <p style="margin: 0; font-size: 0.9rem; color: #666;">Includes complete e-commerce setup with free domain & hosting</p>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-credit-card"></i> Pay ₹75,000 & Start Store
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>WebsiteDeveloper0002.in</h3>
                    <p>Professional web development services for Indian businesses.</p>
                </div>
                <div class="footer-section">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="business-premium.php">Business Premium</a></li>
                        <li><a href="ecommerce.php">E-commerce</a></li>
                        <li><a href="custom-development.php">Custom Development</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +91 XXXXXXXXXX</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
    <script>
        function openPaymentForm() {
            document.getElementById('paymentModal').style.display = 'block';
        }
        
        function closePaymentModal() {
            document.getElementById('paymentModal').style.display = 'none';
        }
        
        // Handle payment form submission
        document.getElementById('ecommerceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate reCAPTCHA
            const recaptchaResponse = grecaptcha.getResponse();
            if (!recaptchaResponse) {
                alert('कृपया reCAPTCHA को complete करें।');
                return;
            }
            
            // Create Razorpay order
            const formData = new FormData(this);
            formData.append('action', 'create_order');
            
            fetch('../payment/razorpay-handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Open Razorpay checkout
                    const options = {
                        key: '<?php echo RAZORPAY_KEY_ID; ?>',
                        amount: data.amount * 100,
                        currency: data.currency,
                        name: data.name,
                        description: data.description,
                        order_id: data.order_id,
                        prefill: data.customer,
                        notes: data.notes,
                        theme: {
                            color: '#667eea'
                        },
                        handler: function(response) {
                            // Verify payment
                            verifyPayment(response);
                        }
                    };
                    
                    const rzp = new Razorpay(options);
                    rzp.open();
                } else {
                    alert(data.message || 'Order creation failed.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('कुछ गलत हुआ है। कृपया पुनः प्रयास करें।');
            });
        });
        
        function verifyPayment(response) {
            const formData = new FormData();
            formData.append('action', 'verify_payment');
            formData.append('razorpay_order_id', response.razorpay_order_id);
            formData.append('razorpay_payment_id', response.razorpay_payment_id);
            formData.append('razorpay_signature', response.razorpay_signature);
            
            fetch('../payment/razorpay-handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = data.redirect_url;
                } else {
                    alert(data.message || 'Payment verification failed.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Payment verification में समस्या हुई।');
            });
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('paymentModal');
            if (event.target === modal) {
                closePaymentModal();
            }
        }
    </script>
</body>
</html>
