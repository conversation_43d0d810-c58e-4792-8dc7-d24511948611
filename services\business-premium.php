<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Get service details
$service = get_service_by_slug('business-premium');
if (!$service) {
    header('HTTP/1.1 404 Not Found');
    include '../404.php';
    exit;
}

$features = json_decode($service['features'], true);
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Premium - Professional Website Development | WebsiteDeveloper0002.in</title>
    <meta name="description" content="Professional business website with query dashboard, email automation, and SEO optimization. Starting at ₹15,000 with free domain & hosting setup.">
    <meta name="keywords" content="business website, professional website, query dashboard, email automation, SEO optimization, web development">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <style>
        .service-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
        }
        
        .service-content {
            padding: 80px 0;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }
        
        .pricing-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 100px;
        }
        
        .price-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .price-amount {
            font-size: 3rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .price-period {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .features-list {
            list-style: none;
            margin-bottom: 2rem;
        }
        
        .features-list li {
            padding: 0.75rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .features-list li:last-child {
            border-bottom: none;
        }
        
        .features-list i {
            color: #28a745;
            font-size: 1.1rem;
        }
        
        .enquiry-form {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .process-section {
            background: #f8f9fa;
            padding: 80px 0;
        }
        
        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .process-step {
            text-align: center;
            position: relative;
        }
        
        .step-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .faq-section {
            padding: 80px 0;
        }
        
        .faq-item {
            background: white;
            margin-bottom: 1rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .faq-question {
            padding: 1.5rem;
            background: #f8f9fa;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            color: #333;
        }
        
        .faq-answer {
            padding: 1.5rem;
            display: none;
            color: #666;
            line-height: 1.6;
        }
        
        .faq-answer.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .service-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .pricing-card {
                position: static;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .process-steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="../index.php">
                        <span class="brand-text">WebsiteDeveloper0002.in</span>
                    </a>
                </div>
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li><a href="../index.php" class="nav-link">होम</a></li>
                        <li><a href="../index.php#services" class="nav-link">सेवाएं</a></li>
                        <li><a href="../index.php#pricing" class="nav-link">प्राइसिंग</a></li>
                        <li><a href="../blog.php" class="nav-link">ब्लॉग</a></li>
                        <li><a href="../index.php#contact" class="nav-link">संपर्क</a></li>
                        <li><a href="../dashboard/login.php" class="nav-link">लॉगिन</a></li>
                    </ul>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Service Hero -->
    <section class="service-hero">
        <div class="container">
            <h1>Business Premium</h1>
            <p>Professional business website with complete management system</p>
            <div class="hero-features">
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Free Domain & Hosting</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Query Dashboard</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Email Automation</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Service Content -->
    <section class="service-content">
        <div class="container">
            <div class="service-grid">
                <div class="service-main">
                    <h2>Complete Business Website Solution</h2>
                    <p>हमारा Business Premium package आपके business के लिए एक complete digital solution है। इसमें professional website के साथ-साथ customer queries को manage करने के लिए एक powerful dashboard भी मिलता है।</p>
                    
                    <h3>क्या मिलता है इस package में:</h3>
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <h4>Professional Website</h4>
                            <p>Multi-page responsive website with modern design और fast loading speed।</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h4>Secure Authentication</h4>
                            <p>Safe और secure login system आपके dashboard के लिए।</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <h4>Query Dashboard</h4>
                            <p>सभी customer inquiries को एक जगह manage करें।</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-envelope-open"></i>
                            </div>
                            <h4>Email Automation</h4>
                            <p>Automatic thank you emails और newsletter system।</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h4>SEO Optimization</h4>
                            <p>Google में better ranking के लिए complete SEO setup।</p>
                        </div>
                        
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h4>Analytics Integration</h4>
                            <p>Google Analytics से track करें आपकी website performance।</p>
                        </div>
                    </div>
                    
                    <h3>Technical Specifications</h3>
                    <ul class="features-list">
                        <li><i class="fas fa-check"></i> Responsive design (mobile, tablet, desktop)</li>
                        <li><i class="fas fa-check"></i> Fast loading speed (under 3 seconds)</li>
                        <li><i class="fas fa-check"></i> SSL certificate included</li>
                        <li><i class="fas fa-check"></i> Contact forms with spam protection</li>
                        <li><i class="fas fa-check"></i> Social media integration</li>
                        <li><i class="fas fa-check"></i> WhatsApp chat integration</li>
                        <li><i class="fas fa-check"></i> Email newsletter signup</li>
                        <li><i class="fas fa-check"></i> Basic content management</li>
                    </ul>
                </div>
                
                <div class="service-sidebar">
                    <div class="pricing-card">
                        <div class="price-header">
                            <div class="price-amount">₹15,000</div>
                            <div class="price-period">one-time payment</div>
                            <p>Complete setup included</p>
                        </div>
                        
                        <ul class="features-list">
                            <?php foreach ($features as $feature): ?>
                                <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <a href="#enquiry" class="btn btn-primary" style="width: 100%; margin-bottom: 1rem;">Enquire Now</a>
                        <a href="tel:+91XXXXXXXXXX" class="btn btn-outline" style="width: 100%;">
                            <i class="fas fa-phone"></i> Call Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Process Section -->
    <section class="process-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">हमारा Development Process</h2>
                <p class="section-subtitle">Step by step जानें कि कैसे हम आपकी website बनाते हैं</p>
            </div>
            
            <div class="process-steps">
                <div class="process-step">
                    <div class="step-number">1</div>
                    <h3>Requirement Analysis</h3>
                    <p>आपकी business requirements को समझना और planning करना।</p>
                </div>
                
                <div class="process-step">
                    <div class="step-number">2</div>
                    <h3>Design & Development</h3>
                    <p>Professional design और development शुरू करना।</p>
                </div>
                
                <div class="process-step">
                    <div class="step-number">3</div>
                    <h3>Testing & Review</h3>
                    <p>Complete testing और आपके साथ review करना।</p>
                </div>
                
                <div class="process-step">
                    <div class="step-number">4</div>
                    <h3>Launch & Support</h3>
                    <p>Website launch करना और ongoing support देना।</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Enquiry Form -->
    <section id="enquiry" class="service-content">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Get Started Today</h2>
                <p class="section-subtitle">अपनी professional website के लिए आज ही enquiry करें</p>
            </div>
            
            <div class="enquiry-form">
                <form id="enquiryForm" action="../includes/contact-handler.php" method="POST">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="name">आपका नाम *</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">ईमेल एड्रेस *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="phone">फोन नंबर *</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="business_type">Business Type</label>
                            <select id="business_type" name="business_type">
                                <option value="">Select Business Type</option>
                                <option value="retail">Retail/Shop</option>
                                <option value="service">Service Provider</option>
                                <option value="restaurant">Restaurant/Food</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="education">Education</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">आपकी Requirements *</label>
                        <textarea id="message" name="message" rows="5" placeholder="बताएं कि आपको कैसी website चाहिए..." required></textarea>
                    </div>
                    
                    <input type="hidden" name="service" value="business">
                    
                    <div class="form-group">
                        <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        Send Enquiry
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Frequently Asked Questions</h2>
                <p class="section-subtitle">Business Premium package के बारे में common questions</p>
            </div>
            
            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>क्या domain और hosting free में मिलता है?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>हाँ, पहले साल के लिए domain registration और hosting बिल्कुल free है। दूसरे साल से आपको renewal करना होगा।</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>Website बनने में कितना समय लगता है?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>आमतौर पर 7-10 working days में आपकी website ready हो जाती है। Complex requirements के case में थोड़ा ज्यादा समय लग सकता है।</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>क्या मैं बाद में changes कर सकता हूँ?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>हाँ, launch के बाद 30 दिन तक minor changes free हैं। बाद में भी reasonable charges पर changes कर सकते हैं।</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question" onclick="toggleFaq(this)">
                    <span>Email automation कैसे काम करता है?</span>
                    <i class="fas fa-chevron-down"></i>
                </div>
                <div class="faq-answer">
                    <p>जब भी कोई customer आपकी website से contact करता है, उन्हें automatically thank you email मिलता है। आपको भी notification मिलता है।</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>WebsiteDeveloper0002.in</h3>
                    <p>Professional web development services for Indian businesses.</p>
                </div>
                <div class="footer-section">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="business-premium.php">Business Premium</a></li>
                        <li><a href="ecommerce.php">E-commerce</a></li>
                        <li><a href="custom-development.php">Custom Development</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +91 XXXXXXXXXX</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
    <script>
        function toggleFaq(element) {
            const answer = element.nextElementSibling;
            const icon = element.querySelector('i');
            
            answer.classList.toggle('active');
            icon.classList.toggle('fa-chevron-down');
            icon.classList.toggle('fa-chevron-up');
        }
    </script>
</body>
</html>
