-- Database schema for websitedeveloper0002.in
-- Create database
CREATE DATABASE IF NOT EXISTS websitedeveloper0002 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE websitedeveloper0002;

-- Users table for authentication
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(15),
    role ENUM('admin', 'user', 'vendor') DEFAULT 'user',
    status ENUM('active', 'inactive', 'pending') DEFAULT 'pending',
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_expires DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact queries table
CREATE TABLE contact_queries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(15),
    service VARCHAR(50),
    message TEXT NOT NULL,
    status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_to INT,
    response TEXT,
    responded_at DATETIME,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
);

-- Services table
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    features JSON,
    price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'INR',
    billing_type ENUM('one_time', 'monthly', 'yearly') DEFAULT 'one_time',
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Blog posts table
CREATE TABLE blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content LONGTEXT NOT NULL,
    excerpt TEXT,
    featured_image VARCHAR(255),
    meta_title VARCHAR(255),
    meta_description TEXT,
    keywords TEXT,
    author_id INT NOT NULL,
    category_id INT,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    published_at DATETIME,
    views INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Blog categories table
CREATE TABLE blog_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    parent_id INT,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES blog_categories(id) ON DELETE SET NULL
);

-- Newsletter subscribers table
CREATE TABLE newsletter_subscribers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(100),
    status ENUM('active', 'unsubscribed', 'bounced') DEFAULT 'active',
    subscription_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribe_token VARCHAR(255),
    ip_address VARCHAR(45),
    source VARCHAR(50) DEFAULT 'website'
);

-- Email templates table
CREATE TABLE email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body LONGTEXT NOT NULL,
    variables JSON,
    type ENUM('transactional', 'marketing') DEFAULT 'transactional',
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Email logs table
CREATE TABLE email_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    to_email VARCHAR(100) NOT NULL,
    from_email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    template_id INT,
    status ENUM('sent', 'failed', 'pending') DEFAULT 'pending',
    error_message TEXT,
    sent_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE SET NULL
);

-- Orders table (for e-commerce)
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id INT,
    customer_name VARCHAR(100) NOT NULL,
    customer_email VARCHAR(100) NOT NULL,
    customer_phone VARCHAR(15),
    service_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'INR',
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    payment_id VARCHAR(100),
    razorpay_order_id VARCHAR(100),
    razorpay_payment_id VARCHAR(100),
    order_status ENUM('new', 'confirmed', 'in_progress', 'completed', 'cancelled') DEFAULT 'new',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
);

-- Settings table
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value LONGTEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- SEO data table
CREATE TABLE seo_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_type VARCHAR(50) NOT NULL,
    page_id INT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,
    og_title VARCHAR(255),
    og_description TEXT,
    og_image VARCHAR(255),
    canonical_url VARCHAR(255),
    robots VARCHAR(100) DEFAULT 'index,follow',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_page (page_type, page_id)
);

-- Insert default admin user
INSERT INTO users (username, email, password, full_name, role, status, email_verified) 
VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'admin', 'active', TRUE);

-- Insert default services
INSERT INTO services (name, slug, description, price, features) VALUES
('Business Premium', 'business-premium', 'Professional business website with query dashboard and email automation', 15000.00, 
'["Free Domain & Hosting Setup", "Multi-page website with secure authentication", "Query Dashboard", "Automatic Email Sender (500 emails/month)", "reCAPTCHA optional", "On-page SEO + Google Analytics", "WhatsApp/Phone/Email support"]'),

('E-commerce', 'ecommerce', 'Complete online store with payment gateway and multi-vendor support', 75000.00,
'["Free Domain & Hosting Setup", "Multi-page storefront with cart & checkout", "Admin, Sellers & Customer Dashboards", "Payment Gateway Integration", "Automatic Email Sender (scalable)", "reCAPTCHA on critical forms", "SEO + Analytics with e-commerce events"]'),

('Custom Development', 'custom-development', 'Tailored solutions for specific business requirements', 0.00,
'["Build anything: CRM, booking, LMS, portals", "Choose features you need", "Pay only for what you use", "Google Analytics integration", "Essential on-page SEO", "Custom automations"]');

-- Insert default email templates
INSERT INTO email_templates (name, slug, subject, body, variables) VALUES
('Contact Thank You', 'contact-thank-you', 'धन्यवाद - आपका संदेश प्राप्त हुआ', 
'<h2>नमस्ते {{name}},</h2>
<p>आपका संदेश हमें प्राप्त हो गया है। हम जल्द ही आपसे संपर्क करेंगे।</p>
<p><strong>आपका संदेश:</strong><br>{{message}}</p>
<p>धन्यवाद,<br>WebsiteDeveloper0002.in Team</p>', 
'["name", "email", "message"]'),

('Newsletter Welcome', 'newsletter-welcome', 'स्वागत है - Newsletter Subscription', 
'<h2>स्वागत है {{name}},</h2>
<p>हमारे newsletter में subscribe करने के लिए धन्यवाद। आपको latest updates और tips मिलते रहेंगे।</p>
<p>Best regards,<br>WebsiteDeveloper0002.in Team</p>', 
'["name", "email"]');

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('site_name', 'WebsiteDeveloper0002.in', 'string', 'Website name'),
('site_description', 'Professional Web Development Services in India', 'string', 'Website description'),
('contact_email', '<EMAIL>', 'string', 'Contact email'),
('contact_phone', '+91 XXXXXXXXXX', 'string', 'Contact phone'),
('whatsapp_number', '+91 XXXXXXXXXX', 'string', 'WhatsApp number'),
('google_analytics_id', '', 'string', 'Google Analytics tracking ID'),
('recaptcha_enabled', 'true', 'boolean', 'Enable reCAPTCHA'),
('email_notifications', 'true', 'boolean', 'Enable email notifications'),
('maintenance_mode', 'false', 'boolean', 'Maintenance mode');
