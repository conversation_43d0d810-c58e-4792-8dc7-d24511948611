<?php
// Create essential database tables
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Tables Creation</h2>";

try {
    require_once 'config/database.php';
    
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Connected to database: " . DB_NAME . "</p>";
    
    // Create users table
    echo "<p>Creating users table...</p>";
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        full_name VARCHAR(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'user') DEFAULT 'user',
        status ENUM('active', 'inactive') DEFAULT 'active',
        remember_token VARCHAR(255) NULL,
        reset_token VARCHAR(255) NULL,
        reset_expires DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Users table created</p>";
    
    // Create contact_queries table
    echo "<p>Creating contact_queries table...</p>";
    $sql = "CREATE TABLE IF NOT EXISTS contact_queries (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL,
        phone VARCHAR(20) NOT NULL,
        service VARCHAR(100) NOT NULL,
        message TEXT NOT NULL,
        status ENUM('new', 'in_progress', 'resolved', 'closed') DEFAULT 'new',
        priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
        response TEXT NULL,
        assigned_to INT NULL,
        responded_at DATETIME NULL,
        ip_address VARCHAR(45) NULL,
        user_agent TEXT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Contact queries table created</p>";
    
    // Create services table
    echo "<p>Creating services table...</p>";
    $sql = "CREATE TABLE IF NOT EXISTS services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT NULL,
        features JSON NULL,
        price DECIMAL(10,2) NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Services table created</p>";
    
    // Create blog_categories table
    echo "<p>Creating blog_categories table...</p>";
    $sql = "CREATE TABLE IF NOT EXISTS blog_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        description TEXT NULL,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Blog categories table created</p>";
    
    // Create blog_posts table
    echo "<p>Creating blog_posts table...</p>";
    $sql = "CREATE TABLE IF NOT EXISTS blog_posts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        excerpt TEXT NULL,
        content LONGTEXT NOT NULL,
        featured_image VARCHAR(255) NULL,
        author_id INT NOT NULL,
        category_id INT NULL,
        status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
        views INT DEFAULT 0,
        published_at DATETIME NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES blog_categories(id) ON DELETE SET NULL
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Blog posts table created</p>";
    
    // Create newsletter_subscribers table
    echo "<p>Creating newsletter_subscribers table...</p>";
    $sql = "CREATE TABLE IF NOT EXISTS newsletter_subscribers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        name VARCHAR(255) NULL,
        status ENUM('active', 'unsubscribed') DEFAULT 'active',
        unsubscribe_token VARCHAR(255) UNIQUE NOT NULL,
        ip_address VARCHAR(45) NULL,
        source VARCHAR(100) DEFAULT 'website',
        subscription_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Newsletter subscribers table created</p>";
    
    // Create orders table
    echo "<p>Creating orders table...</p>";
    $sql = "CREATE TABLE IF NOT EXISTS orders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        order_number VARCHAR(50) UNIQUE NOT NULL,
        customer_name VARCHAR(255) NOT NULL,
        customer_email VARCHAR(255) NOT NULL,
        customer_phone VARCHAR(20) NOT NULL,
        service_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        notes TEXT NULL,
        order_status ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
        payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
        payment_method VARCHAR(50) NULL,
        payment_id VARCHAR(255) NULL,
        razorpay_order_id VARCHAR(255) NULL,
        razorpay_payment_id VARCHAR(255) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE RESTRICT
    )";
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ Orders table created</p>";
    
    // Insert default admin user
    echo "<p>Creating default admin user...</p>";
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $sql = "INSERT IGNORE INTO users (full_name, email, password, role) VALUES (?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['Administrator', '<EMAIL>', $admin_password, 'admin']);
    echo "<p style='color: green;'>✅ Default admin user created (<EMAIL> / admin123)</p>";
    
    // Insert sample services
    echo "<p>Creating sample services...</p>";
    $services = [
        [
            'name' => 'Business Premium',
            'slug' => 'business-premium',
            'description' => 'Professional business website with query dashboard',
            'features' => json_encode([
                'Multi-page responsive website',
                'Contact forms with spam protection',
                'Query management dashboard',
                'Email automation',
                'Free domain & hosting (1st year)',
                'Basic SEO optimization',
                'Google Analytics integration',
                '30 days free support'
            ]),
            'price' => 15000.00
        ],
        [
            'name' => 'E-commerce',
            'slug' => 'ecommerce',
            'description' => 'Complete online store with payment gateway',
            'features' => json_encode([
                'Complete e-commerce website',
                'Payment gateway integration',
                'Product management system',
                'Inventory management',
                'Order management',
                'Multi-vendor support',
                'Sales analytics',
                'Free domain & hosting (1st year)',
                '45 days free support'
            ]),
            'price' => 75000.00
        ],
        [
            'name' => 'Custom Development',
            'slug' => 'custom-development',
            'description' => 'Tailored solutions for specific requirements',
            'features' => json_encode([
                'Custom web applications',
                'CRM systems',
                'Booking platforms',
                'Learning management systems',
                'Business portals',
                'API development',
                'Third-party integrations',
                'Scalable architecture'
            ]),
            'price' => 0.00
        ]
    ];
    
    $sql = "INSERT IGNORE INTO services (name, slug, description, features, price) VALUES (?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);
    foreach ($services as $service) {
        $stmt->execute([$service['name'], $service['slug'], $service['description'], $service['features'], $service['price']]);
    }
    echo "<p style='color: green;'>✅ Sample services created</p>";
    
    echo "<h3 style='color: green;'>🎉 Database setup completed successfully!</h3>";
    echo "<p><strong>You can now access your website:</strong></p>";
    echo "<p><a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Website</a></p>";
    echo "<p><a href='dashboard/login.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Admin Dashboard</a></p>";
    
    echo "<h4>Admin Login Details:</h4>";
    echo "<ul>";
    echo "<li><strong>Email:</strong> <EMAIL></li>";
    echo "<li><strong>Password:</strong> admin123</li>";
    echo "</ul>";
    echo "<p><em>Please change the admin password after first login!</em></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
}

echo "<hr>";
echo "<p><a href='debug-index.php'>← Back to Debug</a></p>";
?>
