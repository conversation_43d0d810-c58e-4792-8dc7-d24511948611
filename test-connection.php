<?php
// Simple database connection test
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Database Connection Test</h2>";

// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'xozhvpdr_wd0002db_user');
define('DB_PASS', ')]rxUCFtCr@*GpeM');
define('DB_NAME', 'xozhvpdr_websitedeveloper0002');

echo "<p><strong>Testing connection with:</strong></p>";
echo "<ul>";
echo "<li>Host: " . DB_HOST . "</li>";
echo "<li>Database: " . DB_NAME . "</li>";
echo "<li>User: " . DB_USER . "</li>";
echo "</ul>";

try {
    echo "<p>Attempting to connect...</p>";
    
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'><strong>✅ Database connection successful!</strong></p>";
    
    // Check if tables exist
    echo "<h3>Checking Tables:</h3>";
    $tables = ['users', 'contact_queries', 'services', 'blog_posts', 'orders', 'newsletter_subscribers'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ Table '$table' exists</p>";
            } else {
                echo "<p style='color: red;'>❌ Table '$table' missing</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error checking table '$table': " . $e->getMessage() . "</p>";
        }
    }
    
    // Test a simple query
    echo "<h3>Testing Query:</h3>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch();
        echo "<p style='color: green;'>✅ Users table query successful. Count: " . $result['count'] . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Query failed: " . $e->getMessage() . "</p>";
        echo "<p><strong>This suggests the database tables haven't been created yet.</strong></p>";
    }
    
} catch(PDOException $e) {
    echo "<p style='color: red;'><strong>❌ Database connection failed:</strong></p>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    
    // Common solutions
    echo "<h3>Possible Solutions:</h3>";
    echo "<ul>";
    echo "<li>Check if the database name is correct</li>";
    echo "<li>Verify username and password</li>";
    echo "<li>Ensure the database exists on your hosting</li>";
    echo "<li>Check if the database user has proper permissions</li>";
    echo "</ul>";
}

echo "<hr>";
echo "<h3>PHP Information:</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>PDO Available: " . (extension_loaded('pdo') ? 'Yes' : 'No') . "</p>";
echo "<p>PDO MySQL Available: " . (extension_loaded('pdo_mysql') ? 'Yes' : 'No') . "</p>";

echo "<hr>";
echo "<p><a href='index.php'>← Back to Website</a></p>";
?>
