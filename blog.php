<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Get page number for pagination
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$posts_per_page = 10;
$offset = ($page - 1) * $posts_per_page;

// Get category filter
$category_id = isset($_GET['category']) ? intval($_GET['category']) : null;

// Get search query
$search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';

// Build query conditions
$where_conditions = ["bp.status = 'published'"];
$params = [];

if ($category_id) {
    $where_conditions[] = "bp.category_id = ?";
    $params[] = $category_id;
}

if ($search) {
    $where_conditions[] = "(bp.title LIKE ? OR bp.content LIKE ? OR bp.excerpt LIKE ?)";
    $search_term = "%{$search}%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = implode(' AND ', $where_conditions);

// Get total posts count for pagination
$count_query = "
    SELECT COUNT(*) 
    FROM blog_posts bp 
    LEFT JOIN blog_categories bc ON bp.category_id = bc.id 
    WHERE {$where_clause}
";
$stmt = $pdo->prepare($count_query);
$stmt->execute($params);
$total_posts = $stmt->fetchColumn();
$total_pages = ceil($total_posts / $posts_per_page);

// Get blog posts
$posts_query = "
    SELECT bp.*, u.full_name as author_name, bc.name as category_name, bc.slug as category_slug
    FROM blog_posts bp 
    LEFT JOIN users u ON bp.author_id = u.id 
    LEFT JOIN blog_categories bc ON bp.category_id = bc.id 
    WHERE {$where_clause}
    ORDER BY bp.published_at DESC 
    LIMIT ? OFFSET ?
";
$params[] = $posts_per_page;
$params[] = $offset;
$stmt = $pdo->prepare($posts_query);
$stmt->execute($params);
$posts = $stmt->fetchAll();

// Get categories for sidebar
$categories = get_blog_categories();

// Get recent posts for sidebar
$recent_posts = get_blog_posts(5);

// SEO meta data
$meta_title = 'Blog - WebsiteDeveloper0002.in';
$meta_description = 'Latest web development tips, tutorials, and insights. Learn about website design, SEO, e-commerce, and digital marketing in Hindi.';
$meta_keywords = 'web development blog, website design tips, SEO tutorials, e-commerce guide, digital marketing, Hindi blog';

if ($search) {
    $meta_title = "Search Results for '{$search}' - Blog";
}

if ($category_id) {
    $current_category = array_filter($categories, function($cat) use ($category_id) {
        return $cat['id'] == $category_id;
    });
    if ($current_category) {
        $current_category = reset($current_category);
        $meta_title = $current_category['name'] . ' - Blog';
        $meta_description = $current_category['description'] ?: $meta_description;
    }
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($meta_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($meta_description); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($meta_keywords); ?>">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .blog-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0 60px;
            text-align: center;
        }
        
        .blog-content {
            padding: 60px 0;
        }
        
        .blog-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .blog-main {
            /* Main content area */
        }
        
        .blog-sidebar {
            /* Sidebar area */
        }
        
        .search-form {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .search-form input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        
        .post-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .post-card:hover {
            transform: translateY(-5px);
        }
        
        .post-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .post-content {
            padding: 1.5rem;
        }
        
        .post-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        .post-category {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            text-decoration: none;
            font-size: 0.8rem;
        }
        
        .post-title {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .post-title a {
            color: #333;
            text-decoration: none;
        }
        
        .post-title a:hover {
            color: #667eea;
        }
        
        .post-excerpt {
            color: #666;
            margin-bottom: 1rem;
            line-height: 1.6;
        }
        
        .read-more {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .read-more:hover {
            text-decoration: underline;
        }
        
        .sidebar-widget {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .widget-title {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .category-list {
            list-style: none;
        }
        
        .category-list li {
            margin-bottom: 0.5rem;
        }
        
        .category-list a {
            color: #666;
            text-decoration: none;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .category-list a:hover {
            background: #f8f9fa;
            color: #667eea;
        }
        
        .recent-post {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .recent-post:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .recent-post-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 5px;
        }
        
        .recent-post-content h4 {
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }
        
        .recent-post-content a {
            color: #333;
            text-decoration: none;
        }
        
        .recent-post-content a:hover {
            color: #667eea;
        }
        
        .recent-post-date {
            font-size: 0.8rem;
            color: #666;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            margin-top: 3rem;
        }
        
        .pagination a,
        .pagination span {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            text-decoration: none;
            color: #333;
        }
        
        .pagination a:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .pagination .current {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .no-posts {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        
        .no-posts i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .blog-container {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .post-meta {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .recent-post {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .recent-post-image {
                width: 100%;
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php">
                        <span class="brand-text">WebsiteDeveloper0002.in</span>
                    </a>
                </div>
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li><a href="index.php" class="nav-link">होम</a></li>
                        <li><a href="index.php#services" class="nav-link">सेवाएं</a></li>
                        <li><a href="index.php#pricing" class="nav-link">प्राइसिंग</a></li>
                        <li><a href="blog.php" class="nav-link">ब्लॉग</a></li>
                        <li><a href="index.php#contact" class="nav-link">संपर्क</a></li>
                        <li><a href="dashboard/login.php" class="nav-link">लॉगिन</a></li>
                    </ul>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Blog Header -->
    <section class="blog-header">
        <div class="container">
            <h1>Web Development Blog</h1>
            <p>Latest tips, tutorials और insights web development की दुनिया से</p>
        </div>
    </section>

    <!-- Blog Content -->
    <section class="blog-content">
        <div class="blog-container">
            <!-- Main Content -->
            <div class="blog-main">
                <?php if (empty($posts)): ?>
                    <div class="no-posts">
                        <i class="fas fa-search"></i>
                        <h3>कोई posts नहीं मिलीं</h3>
                        <p>
                            <?php if ($search): ?>
                                "<?php echo htmlspecialchars($search); ?>" के लिए कोई results नहीं मिले।
                            <?php else: ?>
                                अभी तक कोई blog posts publish नहीं हुई हैं।
                            <?php endif; ?>
                        </p>
                        <?php if ($search): ?>
                            <a href="blog.php" class="btn btn-primary">सभी Posts देखें</a>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <?php foreach ($posts as $post): ?>
                        <article class="post-card">
                            <?php if ($post['featured_image']): ?>
                                <img src="<?php echo htmlspecialchars($post['featured_image']); ?>" alt="<?php echo htmlspecialchars($post['title']); ?>" class="post-image">
                            <?php endif; ?>
                            
                            <div class="post-content">
                                <div class="post-meta">
                                    <?php if ($post['category_name']): ?>
                                        <a href="blog.php?category=<?php echo $post['category_id']; ?>" class="post-category">
                                            <?php echo htmlspecialchars($post['category_name']); ?>
                                        </a>
                                    <?php endif; ?>
                                    <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($post['author_name']); ?></span>
                                    <span><i class="fas fa-calendar"></i> <?php echo date('d M Y', strtotime($post['published_at'])); ?></span>
                                    <span><i class="fas fa-eye"></i> <?php echo $post['views']; ?> views</span>
                                </div>
                                
                                <h2 class="post-title">
                                    <a href="blog/<?php echo htmlspecialchars($post['slug']); ?>.php">
                                        <?php echo htmlspecialchars($post['title']); ?>
                                    </a>
                                </h2>
                                
                                <div class="post-excerpt">
                                    <?php echo htmlspecialchars($post['excerpt'] ?: substr(strip_tags($post['content']), 0, 200) . '...'); ?>
                                </div>
                                
                                <a href="blog/<?php echo htmlspecialchars($post['slug']); ?>.php" class="read-more">
                                    पूरा पढ़ें <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </article>
                    <?php endforeach; ?>
                    
                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?><?php echo $category_id ? '&category=' . $category_id : ''; ?>">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <?php if ($i == $page): ?>
                                    <span class="current"><?php echo $i; ?></span>
                                <?php else: ?>
                                    <a href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?><?php echo $category_id ? '&category=' . $category_id : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endif; ?>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <a href="?page=<?php echo $page + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?><?php echo $category_id ? '&category=' . $category_id : ''; ?>">
                                    Next <i class="fas fa-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <div class="blog-sidebar">
                <!-- Search Widget -->
                <div class="sidebar-widget">
                    <h3 class="widget-title">Search</h3>
                    <form method="GET" action="blog.php" class="search-form">
                        <input type="text" name="search" placeholder="Blog posts में search करें..." value="<?php echo htmlspecialchars($search); ?>">
                        <?php if ($category_id): ?>
                            <input type="hidden" name="category" value="<?php echo $category_id; ?>">
                        <?php endif; ?>
                        <button type="submit" class="btn btn-primary">Search</button>
                    </form>
                </div>
                
                <!-- Categories Widget -->
                <?php if (!empty($categories)): ?>
                    <div class="sidebar-widget">
                        <h3 class="widget-title">Categories</h3>
                        <ul class="category-list">
                            <li>
                                <a href="blog.php">
                                    <span>All Posts</span>
                                    <span><?php echo $total_posts; ?></span>
                                </a>
                            </li>
                            <?php foreach ($categories as $category): ?>
                                <li>
                                    <a href="blog.php?category=<?php echo $category['id']; ?>">
                                        <span><?php echo htmlspecialchars($category['name']); ?></span>
                                    </a>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <!-- Recent Posts Widget -->
                <?php if (!empty($recent_posts)): ?>
                    <div class="sidebar-widget">
                        <h3 class="widget-title">Recent Posts</h3>
                        <?php foreach ($recent_posts as $recent_post): ?>
                            <div class="recent-post">
                                <?php if ($recent_post['featured_image']): ?>
                                    <img src="<?php echo htmlspecialchars($recent_post['featured_image']); ?>" alt="<?php echo htmlspecialchars($recent_post['title']); ?>" class="recent-post-image">
                                <?php endif; ?>
                                <div class="recent-post-content">
                                    <h4>
                                        <a href="blog/<?php echo htmlspecialchars($recent_post['slug']); ?>.php">
                                            <?php echo htmlspecialchars($recent_post['title']); ?>
                                        </a>
                                    </h4>
                                    <div class="recent-post-date">
                                        <?php echo date('d M Y', strtotime($recent_post['published_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                
                <!-- Newsletter Widget -->
                <div class="sidebar-widget">
                    <h3 class="widget-title">Newsletter Subscribe</h3>
                    <p>Latest updates और tips के लिए subscribe करें:</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="आपका email address" required>
                        <button type="submit" class="btn btn-primary">Subscribe</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>WebsiteDeveloper0002.in</h3>
                    <p>Professional web development services for Indian businesses. हम आपके सपनों की website बनाते हैं।</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="index.php#services">Services</a></li>
                        <li><a href="index.php#pricing">Pricing</a></li>
                        <li><a href="blog.php">Blog</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul>
                        <li><a href="legal/privacy-policy.php">Privacy Policy</a></li>
                        <li><a href="legal/terms-conditions.php">Terms & Conditions</a></li>
                        <li><a href="legal/cookie-policy.php">Cookie Policy</a></li>
                        <li><a href="legal/refund-policy.php">Refund Policy</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +91 XXXXXXXXXX</p>
                    <p><i class="fab fa-whatsapp"></i> +91 XXXXXXXXXX</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 WebsiteDeveloper0002.in. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="assets/js/main.js"></script>
</body>
</html>
