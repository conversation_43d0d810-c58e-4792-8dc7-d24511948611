<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Get order number from URL
$order_number = sanitize_input($_GET['order'] ?? '');

if (!$order_number) {
    header('Location: ../index.php');
    exit;
}

// Get order details
$stmt = $pdo->prepare("
    SELECT o.*, s.name as service_name, s.description as service_description 
    FROM orders o 
    LEFT JOIN services s ON o.service_id = s.id 
    WHERE o.order_number = ? AND o.payment_status = 'paid'
");
$stmt->execute([$order_number]);
$order = $stmt->fetch();

if (!$order) {
    header('Location: ../index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - WebsiteDeveloper0002.in</title>
    <meta name="description" content="Payment successful! Your order has been confirmed and we will contact you soon.">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .success-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        
        .success-card {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        
        .success-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            color: white;
            font-size: 3rem;
            animation: successPulse 2s infinite;
        }
        
        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .success-title {
            font-size: 2.5rem;
            color: #28a745;
            margin-bottom: 1rem;
        }
        
        .success-message {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
        }
        
        .order-details {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            text-align: left;
        }
        
        .order-details h3 {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .detail-row:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            font-weight: 600;
            color: #333;
        }
        
        .detail-value {
            color: #666;
        }
        
        .amount-highlight {
            font-size: 1.5rem;
            font-weight: 700;
            color: #28a745;
        }
        
        .next-steps {
            background: #e3f2fd;
            padding: 2rem;
            border-radius: 15px;
            margin: 2rem 0;
            text-align: left;
        }
        
        .next-steps h3 {
            color: #1976d2;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .next-steps ul {
            list-style: none;
            padding: 0;
        }
        
        .next-steps li {
            padding: 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .next-steps i {
            color: #1976d2;
            width: 20px;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        
        .contact-info {
            background: #fff3cd;
            padding: 1.5rem;
            border-radius: 10px;
            margin: 2rem 0;
        }
        
        .contact-info h4 {
            color: #856404;
            margin-bottom: 1rem;
        }
        
        .contact-info p {
            color: #856404;
            margin-bottom: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .success-card {
                padding: 2rem;
                margin: 10px;
            }
            
            .success-title {
                font-size: 2rem;
            }
            
            .success-message {
                font-size: 1rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-card">
            <div class="success-icon">
                <i class="fas fa-check"></i>
            </div>
            
            <h1 class="success-title">Payment Successful!</h1>
            <p class="success-message">
                आपका payment successfully complete हो गया है। हमारी team जल्द ही आपसे संपर्क करेगी।
            </p>
            
            <div class="order-details">
                <h3><i class="fas fa-receipt"></i> Order Details</h3>
                
                <div class="detail-row">
                    <span class="detail-label">Order Number:</span>
                    <span class="detail-value"><?php echo htmlspecialchars($order['order_number']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Service:</span>
                    <span class="detail-value"><?php echo htmlspecialchars($order['service_name']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Customer Name:</span>
                    <span class="detail-value"><?php echo htmlspecialchars($order['customer_name']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Email:</span>
                    <span class="detail-value"><?php echo htmlspecialchars($order['customer_email']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Phone:</span>
                    <span class="detail-value"><?php echo htmlspecialchars($order['customer_phone']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Payment ID:</span>
                    <span class="detail-value"><?php echo htmlspecialchars($order['razorpay_payment_id']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Amount Paid:</span>
                    <span class="detail-value amount-highlight">₹<?php echo number_format($order['amount']); ?></span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">Order Date:</span>
                    <span class="detail-value"><?php echo date('d F Y, h:i A', strtotime($order['created_at'])); ?></span>
                </div>
            </div>
            
            <div class="next-steps">
                <h3><i class="fas fa-list-check"></i> What Happens Next?</h3>
                <ul>
                    <li>
                        <i class="fas fa-phone"></i>
                        हमारी team 24 घंटे के अंदर आपसे संपर्क करेगी
                    </li>
                    <li>
                        <i class="fas fa-comments"></i>
                        Project requirements को detail में discuss करेंगे
                    </li>
                    <li>
                        <i class="fas fa-calendar"></i>
                        Development timeline और milestones share करेंगे
                    </li>
                    <li>
                        <i class="fas fa-laptop-code"></i>
                        आपकी website का development शुरू करेंगे
                    </li>
                    <li>
                        <i class="fas fa-rocket"></i>
                        Testing के बाद website को live करेंगे
                    </li>
                </ul>
            </div>
            
            <div class="contact-info">
                <h4><i class="fas fa-headset"></i> Need Immediate Assistance?</h4>
                <p><i class="fas fa-envelope"></i> Email: <EMAIL></p>
                <p><i class="fas fa-phone"></i> Phone: +91 XXXXXXXXXX</p>
                <p><i class="fab fa-whatsapp"></i> WhatsApp: +91 XXXXXXXXXX</p>
            </div>
            
            <div class="action-buttons">
                <a href="../index.php" class="btn btn-primary">
                    <i class="fas fa-home"></i> Back to Home
                </a>
                <a href="../dashboard/login.php" class="btn btn-outline">
                    <i class="fas fa-tachometer-alt"></i> Access Dashboard
                </a>
                <a href="mailto:<EMAIL>?subject=Order%20<?php echo urlencode($order['order_number']); ?>" class="btn btn-secondary">
                    <i class="fas fa-envelope"></i> Contact Us
                </a>
            </div>
            
            <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #eee; color: #666; font-size: 0.9rem;">
                <p><i class="fas fa-shield-alt"></i> Your payment is secure and protected by Razorpay</p>
                <p><i class="fas fa-certificate"></i> Order confirmation email has been sent to your email address</p>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-redirect to home page after 5 minutes
        setTimeout(function() {
            if (confirm('आप 5 मिनट से इस page पर हैं। क्या आप home page पर जाना चाहते हैं?')) {
                window.location.href = '../index.php';
            }
        }, 300000); // 5 minutes
        
        // Track successful payment
        if (typeof gtag !== 'undefined') {
            gtag('event', 'purchase', {
                'transaction_id': '<?php echo $order['order_number']; ?>',
                'value': <?php echo $order['amount']; ?>,
                'currency': 'INR',
                'items': [{
                    'item_id': '<?php echo $order['service_id']; ?>',
                    'item_name': '<?php echo addslashes($order['service_name']); ?>',
                    'category': 'Web Development',
                    'quantity': 1,
                    'price': <?php echo $order['amount']; ?>
                }]
            });
        }
        
        // Prevent back button after successful payment
        history.pushState(null, null, location.href);
        window.onpopstate = function () {
            history.go(1);
        };
    </script>
</body>
</html>
