<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Get service details
$service = get_service_by_slug('custom-development');
if (!$service) {
    header('HTTP/1.1 404 Not Found');
    include '../404.php';
    exit;
}

$features = json_decode($service['features'], true);
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Development - Tailored Solutions | WebsiteDeveloper0002.in</title>
    <meta name="description" content="Custom web development solutions - CRM, booking systems, LMS, portals. Pay only for what you need. Get a personalized quote today.">
    <meta name="keywords" content="custom development, CRM, booking system, LMS, web portal, custom software, tailored solutions">
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <style>
        .service-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
        }
        
        .custom-badge {
            background: #28a745;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: inline-block;
        }
        
        .service-content {
            padding: 80px 0;
        }
        
        .service-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 3rem;
            margin-bottom: 3rem;
        }
        
        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .solution-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
            border-top: 4px solid #667eea;
        }
        
        .solution-card:hover {
            transform: translateY(-10px);
        }
        
        .solution-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }
        
        .pricing-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 100px;
            text-align: center;
        }
        
        .price-header {
            margin-bottom: 2rem;
        }
        
        .price-text {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .price-subtitle {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .features-list {
            list-style: none;
            margin-bottom: 2rem;
            text-align: left;
        }
        
        .features-list li {
            padding: 0.75rem 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border-bottom: 1px solid #eee;
        }
        
        .features-list li:last-child {
            border-bottom: none;
        }
        
        .features-list i {
            color: #28a745;
            font-size: 1.1rem;
        }
        
        .quote-form {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .checkbox-item input[type="checkbox"] {
            width: auto;
        }
        
        .process-section {
            background: #f8f9fa;
            padding: 80px 0;
        }
        
        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .process-step {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .step-number {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .portfolio-section {
            padding: 80px 0;
        }
        
        .portfolio-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .portfolio-item {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .portfolio-item:hover {
            transform: translateY(-5px);
        }
        
        .portfolio-image {
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }
        
        .portfolio-content {
            padding: 1.5rem;
        }
        
        @media (max-width: 768px) {
            .service-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .pricing-card {
                position: static;
            }
            
            .solutions-grid {
                grid-template-columns: 1fr;
            }
            
            .checkbox-group {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="../index.php">
                        <span class="brand-text">WebsiteDeveloper0002.in</span>
                    </a>
                </div>
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li><a href="../index.php" class="nav-link">होम</a></li>
                        <li><a href="../index.php#services" class="nav-link">सेवाएं</a></li>
                        <li><a href="../index.php#pricing" class="nav-link">प्राइसिंग</a></li>
                        <li><a href="../blog.php" class="nav-link">ब्लॉग</a></li>
                        <li><a href="../index.php#contact" class="nav-link">संपर्क</a></li>
                        <li><a href="../dashboard/login.php" class="nav-link">लॉगिन</a></li>
                    </ul>
                </div>
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Service Hero -->
    <section class="service-hero">
        <div class="container">
            <div class="custom-badge">
                <i class="fas fa-cogs"></i> Fully Customizable
            </div>
            <h1>Custom Development</h1>
            <p>आपके specific requirements के अनुसार tailored solutions</p>
            <div class="hero-features">
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Pay Only for What You Need</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Scalable Solutions</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check-circle"></i>
                    <span>Future-Ready Technology</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Service Content -->
    <section class="service-content">
        <div class="container">
            <div class="service-grid">
                <div class="service-main">
                    <h2>Build Anything You Can Imagine</h2>
                    <p>हमारी Custom Development service आपके unique business requirements के लिए है। चाहे आपको CRM चाहिए, booking system, LMS, या कोई भी specific solution - हम आपके लिए बना सकते हैं।</p>
                    
                    <h3>What We Can Build:</h3>
                    <div class="solutions-grid">
                        <div class="solution-card">
                            <div class="solution-icon">
                                <i class="fas fa-users-cog"></i>
                            </div>
                            <h4>CRM Systems</h4>
                            <p>Customer relationship management के लिए complete CRM solution।</p>
                        </div>
                        
                        <div class="solution-card">
                            <div class="solution-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <h4>Booking Systems</h4>
                            <p>Appointment booking, hotel reservation, या event booking systems।</p>
                        </div>
                        
                        <div class="solution-card">
                            <div class="solution-icon">
                                <i class="fas fa-graduation-cap"></i>
                            </div>
                            <h4>Learning Management</h4>
                            <p>Online courses, training programs के लिए LMS platforms।</p>
                        </div>
                        
                        <div class="solution-card">
                            <div class="solution-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <h4>Business Portals</h4>
                            <p>Employee portals, vendor portals, या customer portals।</p>
                        </div>
                        
                        <div class="solution-card">
                            <div class="solution-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h4>Analytics Dashboards</h4>
                            <p>Business intelligence और data visualization tools।</p>
                        </div>
                        
                        <div class="solution-card">
                            <div class="solution-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <h4>Mobile Apps</h4>
                            <p>Android और iOS apps आपके business के लिए।</p>
                        </div>
                    </div>
                    
                    <h3>Technology Stack</h3>
                    <ul class="features-list">
                        <li><i class="fas fa-check"></i> Frontend: HTML5, CSS3, JavaScript, React, Vue.js</li>
                        <li><i class="fas fa-check"></i> Backend: PHP, Node.js, Python, Laravel, CodeIgniter</li>
                        <li><i class="fas fa-check"></i> Database: MySQL, PostgreSQL, MongoDB</li>
                        <li><i class="fas fa-check"></i> Cloud: AWS, Google Cloud, Azure</li>
                        <li><i class="fas fa-check"></i> Mobile: React Native, Flutter, Native Development</li>
                        <li><i class="fas fa-check"></i> APIs: REST, GraphQL, Third-party integrations</li>
                    </ul>
                </div>
                
                <div class="service-sidebar">
                    <div class="pricing-card">
                        <div class="price-header">
                            <div class="price-text">Price on Request</div>
                            <div class="price-subtitle">Scoped per requirements</div>
                            <p>आपकी जरूरतों के अनुसार pricing</p>
                        </div>
                        
                        <ul class="features-list">
                            <?php foreach ($features as $feature): ?>
                                <li><i class="fas fa-check"></i> <?php echo htmlspecialchars($feature); ?></li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <a href="#quote" class="btn btn-primary" style="width: 100%; margin-bottom: 1rem;">
                            <i class="fas fa-calculator"></i> Get Quote
                        </a>
                        <a href="tel:+91XXXXXXXXXX" class="btn btn-outline" style="width: 100%;">
                            <i class="fas fa-phone"></i> Discuss Project
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Process Section -->
    <section class="process-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Our Development Process</h2>
                <p class="section-subtitle">Step by step कैसे हम आपका custom solution बनाते हैं</p>
            </div>
            
            <div class="process-steps">
                <div class="process-step">
                    <div class="step-number">1</div>
                    <h3>Discovery & Analysis</h3>
                    <p>आपकी requirements को detail में समझना और feasibility analysis।</p>
                </div>
                
                <div class="process-step">
                    <div class="step-number">2</div>
                    <h3>Planning & Design</h3>
                    <p>Technical architecture, UI/UX design, और project timeline planning।</p>
                </div>
                
                <div class="process-step">
                    <div class="step-number">3</div>
                    <h3>Development</h3>
                    <p>Agile methodology के साथ iterative development और regular updates।</p>
                </div>
                
                <div class="process-step">
                    <div class="step-number">4</div>
                    <h3>Testing & QA</h3>
                    <p>Comprehensive testing, bug fixes, और performance optimization।</p>
                </div>
                
                <div class="process-step">
                    <div class="step-number">5</div>
                    <h3>Deployment</h3>
                    <p>Production deployment, training, और documentation handover।</p>
                </div>
                
                <div class="process-step">
                    <div class="step-number">6</div>
                    <h3>Support</h3>
                    <p>Ongoing maintenance, updates, और technical support।</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section class="portfolio-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Previous Custom Projects</h2>
                <p class="section-subtitle">हमारे कुछ successful custom development projects</p>
            </div>
            
            <div class="portfolio-grid">
                <div class="portfolio-item">
                    <div class="portfolio-image">
                        <i class="fas fa-hospital"></i>
                    </div>
                    <div class="portfolio-content">
                        <h4>Hospital Management System</h4>
                        <p>Complete hospital management with patient records, appointment booking, और billing system।</p>
                        <small><strong>Technology:</strong> PHP, MySQL, Bootstrap</small>
                    </div>
                </div>
                
                <div class="portfolio-item">
                    <div class="portfolio-image">
                        <i class="fas fa-school"></i>
                    </div>
                    <div class="portfolio-content">
                        <h4>School Management Portal</h4>
                        <p>Student information system with online classes, assignments, और parent communication।</p>
                        <small><strong>Technology:</strong> Laravel, Vue.js, MySQL</small>
                    </div>
                </div>
                
                <div class="portfolio-item">
                    <div class="portfolio-image">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="portfolio-content">
                        <h4>Inventory Management</h4>
                        <p>Real-time inventory tracking, supplier management, और automated reordering system।</p>
                        <small><strong>Technology:</strong> Node.js, React, MongoDB</small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quote Form -->
    <section id="quote" class="service-content">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Get Your Custom Quote</h2>
                <p class="section-subtitle">बताएं आपको क्या चाहिए, हम आपको detailed quote देंगे</p>
            </div>
            
            <div class="quote-form">
                <form id="quoteForm" action="../includes/contact-handler.php" method="POST">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="name">आपका नाम *</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">ईमेल एड्रेस *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div class="form-group">
                            <label for="phone">फोन नंबर *</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="budget">Approximate Budget</label>
                            <select id="budget" name="budget">
                                <option value="">Select Budget Range</option>
                                <option value="50k-1l">₹50,000 - ₹1,00,000</option>
                                <option value="1l-2l">₹1,00,000 - ₹2,00,000</option>
                                <option value="2l-5l">₹2,00,000 - ₹5,00,000</option>
                                <option value="5l+">₹5,00,000+</option>
                                <option value="discuss">Discuss</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Project Type (select all that apply):</label>
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="crm" name="project_type[]" value="crm">
                                <label for="crm">CRM System</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="booking" name="project_type[]" value="booking">
                                <label for="booking">Booking System</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="lms" name="project_type[]" value="lms">
                                <label for="lms">Learning Management</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="portal" name="project_type[]" value="portal">
                                <label for="portal">Business Portal</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="mobile" name="project_type[]" value="mobile">
                                <label for="mobile">Mobile App</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="other" name="project_type[]" value="other">
                                <label for="other">Other</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">Project Details *</label>
                        <textarea id="message" name="message" rows="6" placeholder="Detail में बताएं कि आपको क्या चाहिए, कैसे काम करना चाहिए, कितने users होंगे, etc..." required></textarea>
                    </div>
                    
                    <input type="hidden" name="service" value="custom">
                    
                    <div class="form-group">
                        <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <i class="fas fa-paper-plane"></i> Get Custom Quote
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>WebsiteDeveloper0002.in</h3>
                    <p>Professional web development services for Indian businesses.</p>
                </div>
                <div class="footer-section">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="business-premium.php">Business Premium</a></li>
                        <li><a href="ecommerce.php">E-commerce</a></li>
                        <li><a href="custom-development.php">Custom Development</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +91 XXXXXXXXXX</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="../assets/js/main.js"></script>
</body>
</html>
