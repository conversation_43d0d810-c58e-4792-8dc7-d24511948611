<?php
session_start();
require_once '../config/database.php';
require_once 'functions.php';

// Set JSON response header
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    json_response(['success' => false, 'message' => 'Invalid request method'], 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        json_response(['success' => false, 'message' => 'Invalid JSON data'], 400);
    }
    
    // Get and sanitize email
    $email = sanitize_input($input['email'] ?? '');
    $name = sanitize_input($input['name'] ?? '');
    
    // Validation
    if (empty($email) || !validate_email($email)) {
        json_response(['success' => false, 'message' => 'कृपया वैध ईमेल पता दर्ज करें।'], 400);
    }
    
    // Rate limiting - check if same IP has subscribed in last hour
    $client_ip = get_client_ip();
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM newsletter_subscribers WHERE ip_address = ? AND subscription_date > DATE_SUB(NOW(), INTERVAL 1 HOUR)");
    $stmt->execute([$client_ip]);
    $recent_subscriptions = $stmt->fetchColumn();
    
    if ($recent_subscriptions >= 5) {
        json_response(['success' => false, 'message' => 'बहुत सारे subscription attempts। कृपया 1 घंटे बाद पुनः प्रयास करें।'], 429);
    }
    
    // Check if email already exists
    $stmt = $pdo->prepare("SELECT * FROM newsletter_subscribers WHERE email = ?");
    $stmt->execute([$email]);
    $existing_subscriber = $stmt->fetch();
    
    if ($existing_subscriber) {
        if ($existing_subscriber['status'] === 'active') {
            json_response(['success' => false, 'message' => 'यह ईमेल पहले से ही newsletter के लिए subscribe है।'], 400);
        } else {
            // Reactivate subscription
            $stmt = $pdo->prepare("UPDATE newsletter_subscribers SET status = 'active', subscription_date = NOW(), ip_address = ? WHERE email = ?");
            $stmt->execute([$client_ip, $email]);
            
            json_response(['success' => true, 'message' => 'Newsletter subscription सफलतापूर्वक reactivate हो गया!']);
        }
    }
    
    // Generate unsubscribe token
    $unsubscribe_token = generate_token(32);
    
    // Insert new subscriber
    $stmt = $pdo->prepare("
        INSERT INTO newsletter_subscribers (email, name, unsubscribe_token, ip_address, source) 
        VALUES (?, ?, ?, ?, 'website')
    ");
    
    $result = $stmt->execute([$email, $name, $unsubscribe_token, $client_ip]);
    
    if (!$result) {
        throw new Exception('Database insertion failed');
    }
    
    // Send welcome email
    $email_template = get_email_template('newsletter-welcome', [
        'name' => $name ?: 'Subscriber',
        'email' => $email,
        'unsubscribe_url' => SITE_URL . '/newsletter/unsubscribe.php?token=' . $unsubscribe_token
    ]);
    
    if ($email_template) {
        send_email($email, $email_template['subject'], $email_template['body'], $email_template['template_id']);
    }
    
    // Send notification to admin
    $admin_email = get_setting('contact_email', '<EMAIL>');
    $admin_subject = 'नया Newsletter Subscriber - ' . $email;
    $admin_body = "
        <h2>नया Newsletter Subscriber</h2>
        <p><strong>ईमेल:</strong> {$email}</p>
        <p><strong>नाम:</strong> " . ($name ?: 'Not provided') . "</p>
        <p><strong>IP Address:</strong> {$client_ip}</p>
        <p><strong>समय:</strong> " . date('Y-m-d H:i:s') . "</p>
        <hr>
        <p>Total active subscribers: " . get_total_subscribers() . "</p>
    ";
    
    send_email($admin_email, $admin_subject, $admin_body);
    
    // Log successful subscription
    error_log("Newsletter subscription successful: {$email}");
    
    // Return success response
    json_response([
        'success' => true,
        'message' => 'Newsletter subscription सफल! Welcome email आपके inbox में भेजा गया है।'
    ]);
    
} catch (Exception $e) {
    // Log error
    error_log("Newsletter subscription error: " . $e->getMessage());
    
    // Return error response
    json_response([
        'success' => false,
        'message' => 'कुछ तकनीकी समस्या हुई है। कृपया बाद में पुनः प्रयास करें।'
    ], 500);
}

function get_total_subscribers() {
    global $pdo;
    $stmt = $pdo->query("SELECT COUNT(*) FROM newsletter_subscribers WHERE status = 'active'");
    return $stmt->fetchColumn();
}
?>
