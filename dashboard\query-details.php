<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require login
require_login();

$user = get_current_user();
$is_admin = $user['role'] === 'admin';

// Get query ID
$query_id = intval($_GET['id'] ?? 0);

if (!$query_id) {
    echo '<p>Invalid query ID.</p>';
    exit;
}

// Get query details
$stmt = $pdo->prepare("
    SELECT cq.*, u.full_name as assigned_name 
    FROM contact_queries cq 
    LEFT JOIN users u ON cq.assigned_to = u.id 
    WHERE cq.id = ?
");
$stmt->execute([$query_id]);
$query = $stmt->fetch();

if (!$query) {
    echo '<p>Query not found.</p>';
    exit;
}

// Check permissions
if (!$is_admin && $query['assigned_to'] && $query['assigned_to'] != $user['id']) {
    echo '<p>You do not have permission to view this query.</p>';
    exit;
}
?>

<div class="query-details">
    <div class="detail-row">
        <span class="detail-label">Name:</span>
        <span class="detail-value"><?php echo htmlspecialchars($query['name']); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Email:</span>
        <span class="detail-value">
            <a href="mailto:<?php echo htmlspecialchars($query['email']); ?>">
                <?php echo htmlspecialchars($query['email']); ?>
            </a>
        </span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Phone:</span>
        <span class="detail-value">
            <a href="tel:<?php echo htmlspecialchars($query['phone']); ?>">
                <?php echo htmlspecialchars($query['phone']); ?>
            </a>
        </span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Service:</span>
        <span class="detail-value"><?php echo htmlspecialchars($query['service']); ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Status:</span>
        <span class="detail-value">
            <span class="status-badge status-<?php echo $query['status']; ?>">
                <?php echo ucfirst(str_replace('_', ' ', $query['status'])); ?>
            </span>
        </span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Priority:</span>
        <span class="detail-value">
            <span class="priority-<?php echo $query['priority']; ?>">
                <?php echo ucfirst($query['priority']); ?>
            </span>
        </span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Assigned To:</span>
        <span class="detail-value"><?php echo $query['assigned_name'] ?: 'Unassigned'; ?></span>
    </div>
    
    <div class="detail-row">
        <span class="detail-label">Created:</span>
        <span class="detail-value"><?php echo date('d M Y, h:i A', strtotime($query['created_at'])); ?></span>
    </div>
    
    <?php if ($query['responded_at']): ?>
        <div class="detail-row">
            <span class="detail-label">Responded:</span>
            <span class="detail-value"><?php echo date('d M Y, h:i A', strtotime($query['responded_at'])); ?></span>
        </div>
    <?php endif; ?>
    
    <div class="detail-row">
        <span class="detail-label">IP Address:</span>
        <span class="detail-value"><?php echo htmlspecialchars($query['ip_address']); ?></span>
    </div>
</div>

<div style="margin: 1.5rem 0;">
    <h4>Customer Message:</h4>
    <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; border-left: 4px solid #667eea;">
        <?php echo nl2br(htmlspecialchars($query['message'])); ?>
    </div>
</div>

<?php if ($query['response']): ?>
    <div style="margin: 1.5rem 0;">
        <h4>Our Response:</h4>
        <div style="background: #e8f5e8; padding: 1rem; border-radius: 5px; border-left: 4px solid #28a745;">
            <?php echo nl2br(htmlspecialchars($query['response'])); ?>
        </div>
    </div>
<?php endif; ?>

<div class="response-form">
    <h4>Update Query</h4>
    <form method="POST" action="queries.php">
        <input type="hidden" name="action" value="update_status">
        <input type="hidden" name="query_id" value="<?php echo $query['id']; ?>">
        
        <div class="form-group">
            <label for="status">Status:</label>
            <select name="status" id="status" required>
                <option value="new" <?php echo $query['status'] === 'new' ? 'selected' : ''; ?>>New</option>
                <option value="in_progress" <?php echo $query['status'] === 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                <option value="resolved" <?php echo $query['status'] === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                <option value="closed" <?php echo $query['status'] === 'closed' ? 'selected' : ''; ?>>Closed</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="response">Response (optional):</label>
            <textarea name="response" id="response" rows="4" placeholder="Type your response to the customer..."></textarea>
            <small style="color: #666;">If you add a response, it will be automatically emailed to the customer.</small>
        </div>
        
        <div style="display: flex; gap: 1rem; justify-content: flex-end;">
            <button type="button" onclick="closeModal()" class="btn btn-outline">Cancel</button>
            <button type="submit" class="btn btn-primary">Update Query</button>
        </div>
    </form>
</div>

<style>
.detail-row {
    display: flex;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #eee;
}

.detail-label {
    font-weight: 600;
    width: 120px;
    color: #333;
}

.detail-value {
    flex: 1;
    color: #666;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-new { background: #fff3cd; color: #856404; }
.status-in_progress { background: #d1ecf1; color: #0c5460; }
.status-resolved { background: #d4edda; color: #155724; }
.status-closed { background: #f8d7da; color: #721c24; }

.priority-high { color: #dc3545; font-weight: 600; }
.priority-medium { color: #ffc107; font-weight: 600; }
.priority-low { color: #28a745; font-weight: 600; }
.priority-urgent { color: #e83e8c; font-weight: 600; }

.response-form {
    border-top: 1px solid #eee;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-outline {
    background: transparent;
    color: #333;
    border: 2px solid #ddd;
}

.btn:hover {
    transform: translateY(-1px);
}
</style>
