<?php
// Common functions for websitedeveloper0002.in

// Security functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function validate_phone($phone) {
    $phone = preg_replace('/[^0-9+]/', '', $phone);
    return preg_match('/^[+]?[0-9]{10,15}$/', $phone);
}

function generate_token($length = 32) {
    return bin2hex(random_bytes($length));
}

function hash_password($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verify_password($password, $hash) {
    return password_verify($password, $hash);
}

// reCAPTCHA verification
function verify_recaptcha($recaptcha_response) {
    if (empty($recaptcha_response)) {
        return false;
    }
    
    $secret_key = RECAPTCHA_SECRET_KEY;
    $verify_url = 'https://www.google.com/recaptcha/api/siteverify';
    
    $data = array(
        'secret' => $secret_key,
        'response' => $recaptcha_response,
        'remoteip' => $_SERVER['REMOTE_ADDR']
    );
    
    $options = array(
        'http' => array(
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        )
    );
    
    $context = stream_context_create($options);
    $result = file_get_contents($verify_url, false, $context);
    $response = json_decode($result, true);
    
    return isset($response['success']) && $response['success'] === true;
}

// Email functions
function send_email($to, $subject, $body, $template_id = null) {
    global $pdo;
    
    try {
        // Log email attempt
        $stmt = $pdo->prepare("INSERT INTO email_logs (to_email, from_email, subject, template_id, status) VALUES (?, ?, ?, ?, 'pending')");
        $stmt->execute([$to, SMTP_FROM_EMAIL, $subject, $template_id]);
        $log_id = $pdo->lastInsertId();
        
        // Use PHPMailer for sending emails
        require_once 'vendor/autoload.php';
        
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = SMTP_PORT;
        $mail->CharSet = 'UTF-8';
        
        // Recipients
        $mail->setFrom(SMTP_FROM_EMAIL, SMTP_FROM_NAME);
        $mail->addAddress($to);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;
        
        $mail->send();
        
        // Update log as sent
        $stmt = $pdo->prepare("UPDATE email_logs SET status = 'sent', sent_at = NOW() WHERE id = ?");
        $stmt->execute([$log_id]);
        
        return true;
        
    } catch (Exception $e) {
        // Update log as failed
        if (isset($log_id)) {
            $stmt = $pdo->prepare("UPDATE email_logs SET status = 'failed', error_message = ? WHERE id = ?");
            $stmt->execute([$e->getMessage(), $log_id]);
        }
        
        error_log("Email sending failed: " . $e->getMessage());
        return false;
    }
}

function get_email_template($slug, $variables = []) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM email_templates WHERE slug = ? AND status = 'active'");
    $stmt->execute([$slug]);
    $template = $stmt->fetch();
    
    if (!$template) {
        return false;
    }
    
    $subject = $template['subject'];
    $body = $template['body'];
    
    // Replace variables in template
    foreach ($variables as $key => $value) {
        $subject = str_replace('{{' . $key . '}}', $value, $subject);
        $body = str_replace('{{' . $key . '}}', $value, $body);
    }
    
    return [
        'subject' => $subject,
        'body' => $body,
        'template_id' => $template['id']
    ];
}

// Database functions
function get_setting($key, $default = null) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    
    return $result ? $result['setting_value'] : $default;
}

function update_setting($key, $value) {
    global $pdo;
    
    $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
    return $stmt->execute([$key, $value, $value]);
}

function get_services($status = 'active') {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM services WHERE status = ? ORDER BY sort_order ASC");
    $stmt->execute([$status]);
    return $stmt->fetchAll();
}

function get_service_by_slug($slug) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM services WHERE slug = ? AND status = 'active'");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

// Blog functions
function get_blog_posts($limit = 10, $offset = 0, $status = 'published') {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT bp.*, u.full_name as author_name, bc.name as category_name 
        FROM blog_posts bp 
        LEFT JOIN users u ON bp.author_id = u.id 
        LEFT JOIN blog_categories bc ON bp.category_id = bc.id 
        WHERE bp.status = ? 
        ORDER BY bp.published_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$status, $limit, $offset]);
    return $stmt->fetchAll();
}

function get_blog_post_by_slug($slug) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT bp.*, u.full_name as author_name, bc.name as category_name 
        FROM blog_posts bp 
        LEFT JOIN users u ON bp.author_id = u.id 
        LEFT JOIN blog_categories bc ON bp.category_id = bc.id 
        WHERE bp.slug = ? AND bp.status = 'published'
    ");
    $stmt->execute([$slug]);
    $post = $stmt->fetch();
    
    if ($post) {
        // Increment view count
        $stmt = $pdo->prepare("UPDATE blog_posts SET views = views + 1 WHERE id = ?");
        $stmt->execute([$post['id']]);
    }
    
    return $post;
}

function get_blog_categories() {
    global $pdo;
    
    $stmt = $pdo->query("SELECT * FROM blog_categories ORDER BY sort_order ASC");
    return $stmt->fetchAll();
}

// User functions
function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function get_current_user() {
    if (!is_logged_in()) {
        return false;
    }
    
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    return $stmt->fetch();
}

function require_login() {
    if (!is_logged_in()) {
        header('Location: /dashboard/login.php');
        exit;
    }
}

function require_admin() {
    $user = get_current_user();
    if (!$user || $user['role'] !== 'admin') {
        header('Location: /dashboard/login.php');
        exit;
    }
}

// Utility functions
function format_currency($amount, $currency = 'INR') {
    if ($currency === 'INR') {
        return '₹' . number_format($amount, 0);
    }
    return $currency . ' ' . number_format($amount, 2);
}

function time_ago($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'अभी';
    if ($time < 3600) return floor($time/60) . ' मिनट पहले';
    if ($time < 86400) return floor($time/3600) . ' घंटे पहले';
    if ($time < 2592000) return floor($time/86400) . ' दिन पहले';
    if ($time < 31536000) return floor($time/2592000) . ' महीने पहले';
    return floor($time/31536000) . ' साल पहले';
}

function generate_slug($text) {
    // Convert to lowercase
    $text = strtolower($text);
    
    // Replace spaces with hyphens
    $text = preg_replace('/\s+/', '-', $text);
    
    // Remove special characters
    $text = preg_replace('/[^a-z0-9\-]/', '', $text);
    
    // Remove multiple hyphens
    $text = preg_replace('/-+/', '-', $text);
    
    // Trim hyphens from ends
    $text = trim($text, '-');
    
    return $text;
}

function get_client_ip() {
    $ip_keys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ip_keys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

function redirect($url, $permanent = false) {
    if ($permanent) {
        header('HTTP/1.1 301 Moved Permanently');
    }
    header('Location: ' . $url);
    exit;
}

function json_response($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// SEO functions
function get_meta_tags($page_type, $page_id = null) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM seo_data WHERE page_type = ? AND page_id = ?");
    $stmt->execute([$page_type, $page_id]);
    return $stmt->fetch();
}

function render_meta_tags($meta_data, $defaults = []) {
    $title = $meta_data['meta_title'] ?? $defaults['title'] ?? get_setting('site_name');
    $description = $meta_data['meta_description'] ?? $defaults['description'] ?? get_setting('site_description');
    $keywords = $meta_data['meta_keywords'] ?? $defaults['keywords'] ?? '';
    $og_title = $meta_data['og_title'] ?? $title;
    $og_description = $meta_data['og_description'] ?? $description;
    $og_image = $meta_data['og_image'] ?? $defaults['image'] ?? '';
    $canonical = $meta_data['canonical_url'] ?? $defaults['canonical'] ?? '';
    $robots = $meta_data['robots'] ?? 'index,follow';
    
    echo "<title>{$title}</title>\n";
    echo "<meta name=\"description\" content=\"{$description}\">\n";
    if ($keywords) echo "<meta name=\"keywords\" content=\"{$keywords}\">\n";
    echo "<meta name=\"robots\" content=\"{$robots}\">\n";
    echo "<meta property=\"og:title\" content=\"{$og_title}\">\n";
    echo "<meta property=\"og:description\" content=\"{$og_description}\">\n";
    echo "<meta property=\"og:type\" content=\"website\">\n";
    if ($og_image) echo "<meta property=\"og:image\" content=\"{$og_image}\">\n";
    if ($canonical) echo "<link rel=\"canonical\" href=\"{$canonical}\">\n";
}
?>
