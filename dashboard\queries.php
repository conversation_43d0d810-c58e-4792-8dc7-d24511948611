<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require login
require_login();

$user = get_current_user();
$is_admin = $user['role'] === 'admin';

// Handle query actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $query_id = intval($_POST['query_id'] ?? 0);
    
    if ($action === 'update_status' && $query_id) {
        $new_status = sanitize_input($_POST['status'] ?? '');
        $response = sanitize_input($_POST['response'] ?? '');
        
        $update_data = ['status' => $new_status];
        $params = [$new_status];
        
        if (!empty($response)) {
            $update_data['response'] = $response;
            $update_data['responded_at'] = 'NOW()';
            $update_data['assigned_to'] = $user['id'];
            $params = [$new_status, $response, $user['id']];
        }
        
        $set_clause = implode(' = ?, ', array_keys($update_data)) . ' = ?';
        if (in_array('NOW()', $update_data)) {
            $set_clause = str_replace("'NOW()'", 'NOW()', $set_clause);
        }
        
        $stmt = $pdo->prepare("UPDATE contact_queries SET {$set_clause} WHERE id = ?");
        $params[] = $query_id;
        
        if ($stmt->execute($params)) {
            $success_message = 'Query updated successfully!';
            
            // Send response email if response was provided
            if (!empty($response)) {
                $stmt = $pdo->prepare("SELECT * FROM contact_queries WHERE id = ?");
                $stmt->execute([$query_id]);
                $query = $stmt->fetch();
                
                if ($query) {
                    $subject = "Response to your inquiry - {$query['name']}";
                    $body = "
                        <h2>Response to Your Inquiry</h2>
                        <p>Dear {$query['name']},</p>
                        <p>Thank you for contacting us. Here's our response to your inquiry:</p>
                        
                        <div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                            <h3>Your Original Message:</h3>
                            <p>{$query['message']}</p>
                        </div>
                        
                        <div style='background: #e8f5e8; padding: 20px; border-radius: 10px; margin: 20px 0;'>
                            <h3>Our Response:</h3>
                            <p>{$response}</p>
                        </div>
                        
                        <p>If you have any further questions, please don't hesitate to contact us.</p>
                        
                        <p>Best regards,<br>
                        {$user['full_name']}<br>
                        WebsiteDeveloper0002.in Team</p>
                    ";
                    
                    send_email($query['email'], $subject, $body);
                }
            }
        } else {
            $error_message = 'Failed to update query.';
        }
    }
}

// Get query details if viewing specific query
$viewing_query = null;
if (isset($_GET['id'])) {
    $query_id = intval($_GET['id']);
    $stmt = $pdo->prepare("
        SELECT cq.*, u.full_name as assigned_name 
        FROM contact_queries cq 
        LEFT JOIN users u ON cq.assigned_to = u.id 
        WHERE cq.id = ?
    ");
    $stmt->execute([$query_id]);
    $viewing_query = $stmt->fetch();
}

// Get filters
$status_filter = $_GET['status'] ?? '';
$service_filter = $_GET['service'] ?? '';
$search = $_GET['search'] ?? '';

// Build query conditions
$where_conditions = [];
$params = [];

if (!$is_admin) {
    $where_conditions[] = "(cq.assigned_to = ? OR cq.assigned_to IS NULL)";
    $params[] = $user['id'];
}

if ($status_filter) {
    $where_conditions[] = "cq.status = ?";
    $params[] = $status_filter;
}

if ($service_filter) {
    $where_conditions[] = "cq.service = ?";
    $params[] = $service_filter;
}

if ($search) {
    $where_conditions[] = "(cq.name LIKE ? OR cq.email LIKE ? OR cq.message LIKE ?)";
    $search_term = "%{$search}%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get queries
$stmt = $pdo->prepare("
    SELECT cq.*, u.full_name as assigned_name 
    FROM contact_queries cq 
    LEFT JOIN users u ON cq.assigned_to = u.id 
    {$where_clause}
    ORDER BY cq.created_at DESC
");
$stmt->execute($params);
$queries = $stmt->fetchAll();

// Get statistics
$stats = [];
$stmt = $pdo->query("SELECT status, COUNT(*) as count FROM contact_queries GROUP BY status");
while ($row = $stmt->fetch()) {
    $stats[$row['status']] = $row['count'];
}
?>
<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Queries Management - WebsiteDeveloper0002.in</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: #2c3e50;
            color: white;
            padding: 1rem;
        }
        
        .sidebar-header {
            text-align: center;
            padding: 1rem 0;
            border-bottom: 1px solid #34495e;
            margin-bottom: 1rem;
        }
        
        .sidebar-nav {
            list-style: none;
        }
        
        .sidebar-nav li {
            margin-bottom: 0.5rem;
        }
        
        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: white;
            text-decoration: none;
            padding: 0.75rem;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background: #34495e;
        }
        
        .main-content {
            flex: 1;
            padding: 2rem;
            background: #f8f9fa;
        }
        
        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .filters {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            align-items: end;
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
        }
        
        .queries-table {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-new { background: #fff3cd; color: #856404; }
        .status-in_progress { background: #d1ecf1; color: #0c5460; }
        .status-resolved { background: #d4edda; color: #155724; }
        .status-closed { background: #f8d7da; color: #721c24; }
        
        .query-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 15px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }
        
        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }
        
        .query-details {
            margin-bottom: 2rem;
        }
        
        .detail-row {
            display: flex;
            margin-bottom: 1rem;
        }
        
        .detail-label {
            font-weight: 600;
            width: 120px;
            color: #333;
        }
        
        .detail-value {
            flex: 1;
            color: #666;
        }
        
        .response-form {
            border-top: 1px solid #eee;
            padding-top: 1.5rem;
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
            }
            
            .filter-row {
                grid-template-columns: 1fr;
            }
            
            .stats-row {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .table {
                font-size: 0.875rem;
            }
            
            .table th,
            .table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>Dashboard</h3>
                <p><?php echo htmlspecialchars($user['full_name']); ?></p>
            </div>
            
            <ul class="sidebar-nav">
                <li><a href="index.php"><i class="fas fa-home"></i> Dashboard</a></li>
                <li><a href="queries.php" class="active"><i class="fas fa-envelope"></i> Queries</a></li>
                <?php if ($is_admin): ?>
                    <li><a href="admin/users.php"><i class="fas fa-users"></i> Users</a></li>
                    <li><a href="admin/blog.php"><i class="fas fa-blog"></i> Blog</a></li>
                    <li><a href="admin/newsletter.php"><i class="fas fa-newspaper"></i> Newsletter</a></li>
                    <li><a href="admin/settings.php"><i class="fas fa-cog"></i> Settings</a></li>
                <?php endif; ?>
                <li><a href="profile.php"><i class="fas fa-user"></i> Profile</a></li>
                <li><a href="logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <div class="page-header">
                <h1>Queries Management</h1>
                <p>Manage customer inquiries and responses</p>
            </div>
            
            <?php if (isset($success_message)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error_message)): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <!-- Statistics -->
            <div class="stats-row">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['new'] ?? 0; ?></div>
                    <div>New Queries</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['in_progress'] ?? 0; ?></div>
                    <div>In Progress</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['resolved'] ?? 0; ?></div>
                    <div>Resolved</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['closed'] ?? 0; ?></div>
                    <div>Closed</div>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="filters">
                <form method="GET" action="">
                    <div class="filter-row">
                        <div class="form-group">
                            <label>Status</label>
                            <select name="status">
                                <option value="">All Status</option>
                                <option value="new" <?php echo $status_filter === 'new' ? 'selected' : ''; ?>>New</option>
                                <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                                <option value="resolved" <?php echo $status_filter === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                                <option value="closed" <?php echo $status_filter === 'closed' ? 'selected' : ''; ?>>Closed</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>Service</label>
                            <select name="service">
                                <option value="">All Services</option>
                                <option value="business" <?php echo $service_filter === 'business' ? 'selected' : ''; ?>>Business Premium</option>
                                <option value="ecommerce" <?php echo $service_filter === 'ecommerce' ? 'selected' : ''; ?>>E-commerce</option>
                                <option value="custom" <?php echo $service_filter === 'custom' ? 'selected' : ''; ?>>Custom Development</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>Search</label>
                            <input type="text" name="search" placeholder="Name, email, or message..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Filter</button>
                            <a href="queries.php" class="btn btn-outline">Clear</a>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Queries Table -->
            <div class="queries-table">
                <?php if (empty($queries)): ?>
                    <div style="padding: 3rem; text-align: center; color: #666;">
                        <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <p>No queries found.</p>
                    </div>
                <?php else: ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Service</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Assigned To</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($queries as $query): ?>
                                <tr>
                                    <td>
                                        <strong><?php echo htmlspecialchars($query['name']); ?></strong><br>
                                        <small><?php echo htmlspecialchars($query['email']); ?></small><br>
                                        <small><?php echo htmlspecialchars($query['phone']); ?></small>
                                    </td>
                                    <td><?php echo htmlspecialchars($query['service']); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $query['status']; ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $query['status'])); ?>
                                        </span>
                                    </td>
                                    <td><?php echo time_ago($query['created_at']); ?></td>
                                    <td><?php echo $query['assigned_name'] ?: 'Unassigned'; ?></td>
                                    <td>
                                        <button onclick="viewQuery(<?php echo $query['id']; ?>)" class="btn btn-outline btn-sm">
                                            <i class="fas fa-eye"></i> View
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Query Modal -->
    <div id="queryModal" class="query-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Query Details</h2>
                <button class="close-modal" onclick="closeModal()">&times;</button>
            </div>
            <div id="modalBody">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script>
        function viewQuery(queryId) {
            fetch(`query-details.php?id=${queryId}`)
                .then(response => response.text())
                .then(html => {
                    document.getElementById('modalBody').innerHTML = html;
                    document.getElementById('queryModal').style.display = 'block';
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to load query details.');
                });
        }
        
        function closeModal() {
            document.getElementById('queryModal').style.display = 'none';
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('queryModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
